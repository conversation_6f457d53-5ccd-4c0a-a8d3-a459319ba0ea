const moment = require("moment");
const EMAIL_TO = process.env.GPS_EXTRACT_CRON_EMAIL_TO ? process.env.GPS_EXTRACT_CRON_EMAIL_TO.split(",") : "<EMAIL>";

const CRON_TIME =  "0 0 0 1 * *";
const LOG_TYPE = "feasibility check";

console.log(`[GPS EXTRACT]: Gps Extract via cron is enabled. Send to Email ${EMAIL_TO}`);

module.exports = {
  gpsExtract: {
    task: async ({ strapi }) => {
      const dateFrom =  moment().subtract('1', 'months').startOf('month').set('hours', 0).set('minutes', 0).set('seconds', 0).format('YYYY-MM-DD HH:mm:ss');
      const dateTo = moment().subtract('1', 'months').endOf('month').set('hours', 23).set('minutes', 59).set('seconds', 59).format('YYYY-MM-DD HH:mm:ss');

      console.log(`START GPS EXTRACT ${dateFrom} - ${dateTo}`);

      const response = await strapi.plugins["gps-extract"].services.myLogService.findData(dateFrom, dateTo, EMAIL_TO, LOG_TYPE);

      if (response.hasOwnProperty('errors')) {
        console.error(`[GPS EXTRACT]: Email is not send. Message: ${response.errors}`);
      } else if (response.hasOwnProperty('arrayIsEmpty')) {
        console.log(`[GPS EXTRACT]: Email is not send. No results found`);
      } else {
        console.log(`[GPS EXTRACT]: Successfully sent.`);
      }
      return response;

    },
    options: {
      rule: CRON_TIME,
    },
  },
};
