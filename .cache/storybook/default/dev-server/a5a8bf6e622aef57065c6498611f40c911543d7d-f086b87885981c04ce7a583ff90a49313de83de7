{"value": {"scaffolded-empty": {"body": {"eventType": "scaffolded-empty", "eventId": "5edGOk57s35MZ8YPYpjrd", "sessionId": "5YveqjOPuAuT-650U42kE", "payload": {"packageManager": "npm", "projectType": "vue-vite-ts", "metadataErrorMessage": "No configuration files have been found in your configDir: .storybook.\nStorybook needs a \"main.js\" file, please add it.\n\nYou can pass a --config-dir flag to tell Storybook, where your main.js file is located at).\n\nMore info: https://storybook.js.org/docs/configure\n"}, "context": {"inCI": false, "isTTY": true, "platform": "Linux", "cliVersion": "8.1.7"}}, "timestamp": 1718201130416}, "init": {"body": {"eventType": "init", "eventId": "Qw6ma1HI4O1V7Wtdn9hBt", "sessionId": "5YveqjOPuAuT-650U42kE", "metadata": {"generatedAt": 1718201223913, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": false, "refCount": 0, "testPackages": {}, "packageManager": {"type": "npm", "version": "9.5.1"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/vue3-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/vue3", "storybookVersion": "8.1.7", "storybookVersionSpecifier": "^8.1.7", "language": "typescript", "storybookPackages": {"@storybook/blocks": {"version": "8.1.7"}, "@storybook/test": {"version": "8.1.7"}, "@storybook/vue3": {"version": "8.1.7"}, "@storybook/vue3-vite": {"version": "8.1.7"}, "storybook": {"version": "8.1.7"}}, "addons": {"@storybook/addon-links": {"version": "8.1.7"}, "@storybook/addon-essentials": {"version": "8.1.7"}, "@chromatic-com/storybook": {"version": "1.5.0"}, "@storybook/addon-interactions": {"version": "8.1.7"}}}, "payload": {"projectType": "VUE3"}, "context": {"inCI": false, "isTTY": true, "platform": "Linux", "cliVersion": "8.1.7"}}, "timestamp": 1718201225730}}, "type": "Object", "created": "2024-06-12T14:07:05.730Z", "ttl": 0}