/** @type {import('vite').UserConfig} */
import { defineConfig } from 'vite'
import commonjs from 'vite-plugin-commonjs'
import vue from '@vitejs/plugin-vue'
// import Unlighthouse from '@unlighthouse/vite'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
	plugins: [
		vue({
			template: {
				compilerOptions: {
					// compatConfig: {
					// 	MODE: 2,
					// 	COMPILER_V_BIND_OBJECT_ORDER: false,
					// },
					isCustomElement: (tag) => tag.includes('sl-') || tag == 'marquee'
				}
			}
		}),
		commonjs(),
		// Unlighthouse({
		//  site: 'http://localhost:5173/',
		// })
	],
	resolve: {
		alias: {
			"@": path.resolve(__dirname, "./src"),
			"~": 'design-system'
		},
	},
	define: {
		// enable hydration mismatch details in production build
		__VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'true'
	},
	optimizeDeps: {
		include: [
			"vue-google-maps-community-fork",
			"fast-deep-equal",
		],
	},
})
