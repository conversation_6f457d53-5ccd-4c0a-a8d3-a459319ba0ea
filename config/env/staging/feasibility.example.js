module.exports = ({ env }) => ({
  baseUrl: "https://solid2.cybersmart.co.za/cybersmart/",
  distributionNodeKey: "",
  cybersmartAPIKey: "",
  onlineOrderPlacementAPIKey: "",
  createZoneRecordAPIKey: "",
  purchaseApp: "https://solid2.cybersmart.co.za/cybersmart/rest/api/webapp/v2/search-api/masteraccountsearch",
  authService: "http://localhost:3000/getAuth",
  searchApiAuthKeyPurchase: "",
  searchApiAuthKey: "",

  feasibility: {
    url: env("FEASIBILITY_API_URL", "https://solid2.cybersmart.co.za/cybersmart/fibre/feasibility"),
    authorization:env("FEASIBILITY_API_AUTHORIZATION", ""),
    rangeKm: env("FEASIBILITY_API_RANGE", ""),
    authKey:env("FEASIBILITY_API_AUTH_KEY", "")
  },

  zonelookup: {
    url: env("FEASIBILITY_API_URL", "https://solid2.cybersmart.co.za/cybersmart/fibre/nodeinfo"),
    authorization: env("FEASIBILITY_API_AUTHORIZATION", ""),
    authKey: env("FEASIBILITY_API_AUTH_KEY", "")
  },

  address: {
    url: env("ADDRESS_API_URL", "https://solid2.cybersmart.co.za/cybersmart/fibre/nodeunits"),
    authorization: env("ADDRESS_API_AUTHORIZATION", ""),
    authKey: env("ADDRESS_API_AUTH_KEY", "")
  },

  customer: {
    url: env("CUSTOMER_API_URL", "https://solid2.cybersmart.co.za/cybersmart/api/masteraccount?api=create_master"),
    authorization: env("CUSTOMER_API_AUTHORIZATION", ""),
    authKey: env("CUSTOMER_API_AUTH_KEY", "")
  },

  bank: {
    url: env("CUSTOMER_API_URL", "https://solid2.cybersmart.co.za/cybersmart/api/masteraccount?api=set_master_payment"),
    authorization: env("CUSTOMER_API_AUTHORIZATION", ""),
    authKey: env("ADDRESS_API_AUTH_KEY", "")
  },

  order: {
    url: env("ORDER_API_URL", "https://solid2.cybersmart.co.za/cybersmart/api/createorder"),
    authorization: env("ORDER_API_AUTHORIZATION", ""),
    authKey: env("ORDER_API_AUTH_KEY", "")
  },

  addaddress: {
    url: env("ADD_ADDRESS_URL", "https://solid2.cybersmart.co.za/cybersmart/fibre/nodeunits/create"),
    authorization: env("ADD_ADDRESS_AUTHORIZATION", ""),
    authKey: env("ADD_ADDRESS_AUTH_KEY", "")
  },

  document: {
    url: env("ADD_CUSTOMER_DOCUMENT", "https://solid2.cybersmart.co.za/cybersmart/rest/api/webapp/document/upload/customer"),
    authKey: env("ADD_CUSTOMER_DOCUMENT_AUTH_KEY", "")
  },

  createContact: {
    url: env("CREATE_CONTACT_URL", "https://solid2.cybersmart.co.za/cybersmart/rest/api/webapp/v2/contact-api/contact.CreateContact"),
    authKey: env("CREATE_CONTACT_AUTHORIZATION", "")
  },



});
