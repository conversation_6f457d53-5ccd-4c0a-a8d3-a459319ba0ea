module.exports = ({ env }) => ({
  emailByBrand: {
    cybersmart: "<EMAIL>",
    ftta: "<EMAIL>, <EMAIL>",
    "cybersmartFttb": "<EMAIL>",
    openserveDsl: "<EMAIL>",
    other: "<EMAIL>",
  },
  fromEmailByBrand: {
    cybersmart: "<EMAIL>",
    ftta: "<EMAIL>",
    other: "<EMAIL>",
  },
  failureReportRecipient: {
    to: "<EMAIL>",
    cc: "<EMAIL>",
    bcc: "<EMAIL>",
  },
  upgradeRequestRecipient: {
    to: "<EMAIL>",
    bcc: "<EMAIL>",
  }
});
