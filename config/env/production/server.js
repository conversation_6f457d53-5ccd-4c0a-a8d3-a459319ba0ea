const { gpsExtract } = require("../../../cron-tasks/gps-extract");

module.exports = ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  app: {
    keys: env.array('APP_KEYS'),
  },
  webhooks: {
    populateRelations: env.bool('WEBHOOKS_POPULATE_RELATIONS', false),
  },
  url: 'http://production2.cybersmart.co.za:1337',
  cron: {
    enabled: true,
    tasks: {
      gpsExtract
    }
  },
});
