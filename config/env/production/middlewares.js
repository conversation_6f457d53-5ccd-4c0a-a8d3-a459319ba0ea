module.exports = [
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      xssFilter: true
    }
  },
  {
    name: 'strapi::cors',
    config: {
      enabled: true,
      origin: "*",
      expose: [
        "WWW-Authenticate",
        "Server-Authorization"
      ],
      maxAge: 31536000,
      credentials: true,
      methods: [
        "GET",
        "POST",
        "PUT",
        "PATCH",
        "DELETE",
        "OPTIONS",
        "HEAD"
      ],
      headers: [
        "Content-Type",
        "Authorization",
        "X-Frame-Options",
        "Origin"
      ]
    }
  },
  'strapi::poweredBy',
  'strapi::query',
  {
    name: 'strapi::logger',
    config: {
      exposeInContext: true,
      requests: true
    }
  },
  {
    name: 'strapi::body',
    config: {
      parsedMethods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"],
      patchKoa: true,
    },
  },
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
