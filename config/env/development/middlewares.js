module.exports = [
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      xssFilter: true
    }
  },
  'strapi::cors',
  'strapi::poweredBy',
  'strapi::query',
  {
    name: 'strapi::logger',
    config: {
      exposeInContext: true,
      requests: true
    }
  },
  {
    name: 'strapi::body',
    config: {
      parsedMethods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"],
      patchKoa: true,
    },
  },
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
