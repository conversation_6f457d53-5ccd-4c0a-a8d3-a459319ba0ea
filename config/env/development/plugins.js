module.exports = ({ env }) => ({
  'gps-extract': {
    enabled: true,
    resolve: './src/plugins/gps-extract'
  },
  email: {
    config: {
      provider: 'nodemailer',
      providerOptions: {
        host: env('SMTP_HOST', 'smtp-cp.cybersmart.co.za'),
        port: env('SMTP_PORT', 25),
        secure: false, // true for 465, false for other ports
        ignoreTLS: true, // ignore server support for STARTTLS
        requireTLS: false, // do not require TLS
        rejectUnauthorized: false, // ignore certificate validation errors
        auth: {
          user: env('SMTP_USERNAME'),
          pass: env('SMTP_PASSWORD'),
        },
      },
      settings: {
        defaultFrom: '<EMAIL>',
        defaultReplyTo: '<EMAIL>',
      },
    }
  }
});
