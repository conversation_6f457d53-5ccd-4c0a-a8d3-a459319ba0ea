{"name": "cybersmart-backend-v-3", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop --debug", "start": "strapi start", "start_log": "NODE_ENV=staging strapi start >> ../logs/new_backend01.txt 2>&1 &", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"@strapi/plugin-seo": "^1.9.8", "@strapi/plugin-users-permissions": "^4.25.7", "@strapi/provider-email-nodemailer": "^4.25.7", "@strapi/strapi": "^4.25.7", "better-sqlite3": "8.0.1", "codemirror": "^6.0.1", "csv-writer": "^1.6.0", "flat": "^5.0.2", "log-timestamp": "^0.3.0", "moment": "^2.30.1", "node-fetch": "^3.3.2", "pg": "^8.11.5", "puppeteer": "^23.9.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "5.3.4", "request": "^2.88.2", "request-promise": "^4.2.6", "showdown": "^2.1.0", "strapi-plugin-ezforms": "^0.1.7", "strapi-plugin-menus": "^1.6.1", "strapi-plugin-populate-deep": "^3.0.1", "strapi-plugin-schemas-to-ts": "^1.3.2", "strong-soap": "^4.0.5", "styled-components": "5.3.3"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "221dba82-b726-4e15-ac61-331c9a74a41e"}, "engines": {"node": ">=14.19.1 <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.25.2", "@babel/runtime": "^7.25.0", "@codemirror/autocomplete": "^6.18.0", "@codemirror/language": "^6.10.2", "@codemirror/lint": "^6.8.1", "@codemirror/search": "^6.5.6", "@codemirror/state": "^6.4.1", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.32.0", "@lezer/common": "^1.2.1", "@strapi/design-system": "^1.19.0", "@strapi/helper-plugin": "^4.25.8", "@strapi/icons": "^1.19.0", "@strapi/utils": "^4.25.8", "eslint": "^8.0.0-0", "formik": "^2.4.6", "lodash": "^4.17.21", "prop-types": "^15.8.1", "react-intl": "^6.6.8", "react-is": "^18.3.1"}}