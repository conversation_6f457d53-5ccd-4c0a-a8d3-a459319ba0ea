{"name": "design-system", "private": true, "version": "0.0.1", "type": "module", "main": "src/index.ts", "styles": "dist/assets/index.css", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=chpt_c63233bfa4f2030"}, "dependencies": {"@headlessui/vue": "^1.7.22", "@shoelace-style/shoelace": "^2.15.1", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vue": "^3.4.21"}, "devDependencies": {"@chromatic-com/storybook": "^1.6.1", "@storybook/addon-a11y": "^8.2.4", "@storybook/addon-essentials": "^8.2.4", "@storybook/addon-interactions": "^8.2.4", "@storybook/addon-links": "^8.2.4", "@storybook/blocks": "^8.2.4", "@storybook/test": "^8.2.4", "@storybook/vue3": "^8.2.4", "@storybook/vue3-vite": "^8.2.4", "@vitejs/plugin-vue": "^5.0.4", "chromatic": "^11.5.4", "storybook": "^8.2.4", "tailwindcss-3d": "^1.0.6", "typescript": "^5.2.2", "vite": "^5.2.0", "vue-tsc": "^2.0.6"}}