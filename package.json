{"name": "cybermart", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview"}, "dependencies": {"@gtm-support/vue-gtm": "^3.0.1", "@sendinblue/client": "^3.3.1", "@splidejs/vue-splide": "^0.6.12", "@unhead/vue": "^1.9.14", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "axios": "^1.7.2", "design-system": "git+https://github.com/platinumseed/cybersmart-design-system.git", "dotenv": "^8.2.0", "lodash": "^4.17.19", "marked": "^0.6.2", "moment": "^2.24.0", "vite-plugin-commonjs": "^0.10.1", "vue": "^3.1.0", "vue-axios": "^3.5.2", "vue-google-maps-community-fork": "^0.3.1", "vue-moment": "^4.1.0", "vue-recaptcha-v3": "^2.0.1", "vue-router": "^4.3.3", "vue-tel-input": "9.1.4", "vue-tsc": "^2.0.22", "vuex": "^4.1.0"}, "devDependencies": {"@unlighthouse/vite": "^0.10.6", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.19", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "^5.0.0"}}