module.exports = {
	content: [
		'./index.html',
		'./src/**/*.{vue,js,ts,jsx,tsx}',
		'node_modules/design-system/src/**/*.{vue,js,ts,jsx,tsx}', // Include design system files
	],
	theme: {
		fontFamily: {
			'roboto': ['Roboto', 'sans-serif'],
		},
		container: {
			center: true,
			padding: {
				DEFAULT: '1rem',
				lg: '2rem',
			},
		},
		extend: {
			colors: {
				'blue': '#209FD6',
				'navy': '#021B31',
				'light-blue': '#F0FAFE',
				'digital-black': '#231F20',
				'teal': '#AEF8D8' 
			},
		},
	},
	plugins: [],
}
