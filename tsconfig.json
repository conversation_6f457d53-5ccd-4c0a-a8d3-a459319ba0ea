{
	"compilerOptions": {
		"baseUrl": ".",
		"paths": {
			"@/*": [
				"src/*"
			]
		},
		"types": [
			"./types"
		], // Ensure global types are included
		"moduleResolution": "node",
		"esModuleInterop": true,
		"strict": true,
		"jsx": "preserve",
		"lib": [
			"esnext",
			"dom"
		],
		"module": "esnext",
		"target": "esnext"
	},
	"include": [
		"src",
		"types"
	], // Move `include` outside `compilerOptions`
	"exclude": [
		"node_modules"
	] // You can also add an `exclude` section if needed
}