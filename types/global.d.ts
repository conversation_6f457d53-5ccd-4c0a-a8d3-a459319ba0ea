// types/global.d.ts
export { };

declare global {
	enum ButtonType {
		Primary = 'primary',
		Secondary = 'secondary',
		Outline = 'outline',
		Ghost = 'ghost',
	}

	interface Button {
		id: number
		label?: string
		type: ButtonType
		href?: string
		iconBefore?: string
		iconAfter?: string
		url: string
	}

	interface Button_Plain {
		label?: string;
		url?: string;
		type?: ButtonType;
		iconBefore?: string;
		iconAfter?: string;
	}

	interface sectionIntro {
		subtitle: string
		title: string
		description: string
	}

	interface featureCard {
		id: number
		title: string
		description: string
		icon: string
		linkText: string
		linkUrl: string
		textAlign: 'left' | 'center'
		iconStyle: 'boxed' | 'open'
	}

	interface Image {
		url: string
		alternativeText: string
	}

	interface Breadcrumb {
		label: string
		url?: string
	}

	interface Package {
		id: number
		title: string
		description?: string | null
		cost?: string | null
		ctaLabel?: string | null
		ctaUrl?: string | null
		ribbonText?: string | null
		ribbonIcon?: string | null
	}

	interface RTEContent {
		id: number,
		type: string,
		level?: number,
		children: object[]
	}

	interface Child {
		type: string,
		text?: string,
		children?: Child[]
	}

	export interface TestimonialCard {
		author: string;
		testimonial: string;
		location?: string;
		rating?: number;
	}
	
	interface Testimonial {
		id: number;
		attributes: {
			createdAt: Date;
			updatedAt: Date;
			publishedAt?: Date;
			testimonial: TestimonialCard;
			image: {
				data: Media
			}
		};
	}

	interface NavigationItem {
		id: number
		attributes: {
			order: number
			title: string
			url: string
			target?: string
			icon?: string
			createdAt: Date;
			updatedAt: Date;
			children: {
				data: NavigationItem[]
			}
		}
	}

	interface Navigation {
		id: number
		attributes: {
			title: string
			slug: string
			createdAt: Date;
			updatedAt: Date;
			children: {
				data: NavigationItem[]
			}
		}
	}
}