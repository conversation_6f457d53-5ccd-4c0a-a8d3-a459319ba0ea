import type { Schema, Attribute } from '@strapi/strapi';

export interface SharedSeo extends Schema.Component {
  collectionName: 'components_shared_seos';
  info: {
    displayName: 'seo';
    icon: 'search';
  };
  attributes: {
    metaTitle: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 60;
      }>;
    metaDescription: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 50;
        maxLength: 160;
      }>;
    metaImage: Attribute.Media<'images' | 'files' | 'videos'>;
    metaSocial: Attribute.Component<'shared.meta-social', true>;
    keywords: Attribute.Text;
    metaRobots: Attribute.String;
    structuredData: Attribute.JSON;
    metaViewport: Attribute.String;
    canonicalURL: Attribute.String;
  };
}

export interface SharedMetaSocial extends Schema.Component {
  collectionName: 'components_shared_meta_socials';
  info: {
    displayName: 'metaSocial';
    icon: 'project-diagram';
  };
  attributes: {
    socialNetwork: Attribute.Enumeration<['Facebook', 'Twitter']> &
      Attribute.Required;
    title: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 60;
      }>;
    description: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 65;
      }>;
    image: Attribute.Media<'images' | 'files' | 'videos'>;
  };
}

export interface ComponentsTestimonialCard extends Schema.Component {
  collectionName: 'components_components_testimonial_cards';
  info: {
    displayName: 'Testimonial Card';
    icon: 'quote';
  };
  attributes: {
    author: Attribute.String & Attribute.Required;
    testimonial: Attribute.Text & Attribute.Required;
    location: Attribute.String;
    rating: Attribute.Integer &
      Attribute.SetMinMax<
        {
          max: 5;
        },
        number
      >;
  };
}

export interface ComponentsPackageCard extends Schema.Component {
  collectionName: 'components_components_package_cards';
  info: {
    displayName: 'Package Card';
    icon: 'priceTag';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.String;
    cost: Attribute.String;
    ctaLabel: Attribute.String;
    ctaUrl: Attribute.String;
    ribbonText: Attribute.String;
    ribbonIcon: Attribute.String;
  };
}

export interface ComponentsLinkCard extends Schema.Component {
  collectionName: 'components_components_link_cards';
  info: {
    displayName: 'Link Card';
    icon: 'link';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    descripion: Attribute.Text;
    url: Attribute.String & Attribute.Required;
    urlTarget: Attribute.Enumeration<['_self', '_blank', '_parent', '_top']> &
      Attribute.DefaultTo<'_self'>;
    featureList: Attribute.JSON;
  };
}

export interface ComponentsHomeConnectivityHero extends Schema.Component {
  collectionName: 'components_components_home_connectivity_heroes';
  info: {
    displayName: 'Home Connectivity Hero';
    icon: 'alien';
  };
  attributes: {
    image: Attribute.Media<'images'> & Attribute.Required;
  };
}

export interface ComponentsFeatureCard extends Schema.Component {
  collectionName: 'components_blocks_feature_cards';
  info: {
    displayName: 'Feature Card';
    icon: 'monitor';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Text;
    icon: Attribute.String;
    linkText: Attribute.String;
    linkUrl: Attribute.String;
    iconStyle: Attribute.Enumeration<['boxed', 'open']> &
      Attribute.DefaultTo<'boxed'>;
    textAlign: Attribute.Enumeration<['left', 'center']> &
      Attribute.DefaultTo<'center'>;
  };
}

export interface ComponentsCtaCard extends Schema.Component {
  collectionName: 'components_blocks_cta_cards';
  info: {
    displayName: 'CTA Card';
    icon: 'calendar';
    description: '';
  };
  attributes: {
    title: Attribute.String & Attribute.Required;
    description: Attribute.String;
    tags: Attribute.JSON;
    image: Attribute.Media<'images'>;
    url: Attribute.String & Attribute.Required;
  };
}

export interface ComponentsButton extends Schema.Component {
  collectionName: 'components_blocks_buttons';
  info: {
    displayName: 'Button';
    icon: 'write';
    description: '';
  };
  attributes: {
    label: Attribute.String;
    url: Attribute.String;
    type: Attribute.Enumeration<['primary', 'secondary', 'outline', 'ghost']>;
    iconBefore: Attribute.String;
    iconAfter: Attribute.String;
  };
}

export interface ComponentsBreadcrumbLink extends Schema.Component {
  collectionName: 'components_components_breadcrumb_links';
  info: {
    displayName: 'Breadcrumb Link';
    icon: 'link';
  };
  attributes: {
    label: Attribute.String & Attribute.Required;
    url: Attribute.String;
  };
}

export interface ComponentsAccordion extends Schema.Component {
  collectionName: 'components_components_accordions';
  info: {
    displayName: 'Accordion';
    icon: 'bulletList';
  };
  attributes: {
    label: Attribute.String & Attribute.Required;
    content: Attribute.Blocks & Attribute.Required;
  };
}

export interface Components2ColImageWithText extends Schema.Component {
  collectionName: 'components_components_2_col_image_with_texts';
  info: {
    displayName: '2 Col Image with text';
    icon: 'picture';
  };
  attributes: {
    image: Attribute.Media<'images'> & Attribute.Required;
    imageAlign: Attribute.Enumeration<['left', 'right']> &
      Attribute.DefaultTo<'right'>;
    content: Attribute.Blocks & Attribute.Required;
    icon: Attribute.String;
    button: Attribute.Component<'components.button', true>;
  };
}

export interface BlocksTestimonialSlider extends Schema.Component {
  collectionName: 'components_blocks_testimonial_sliders';
  info: {
    displayName: 'Testimonial Slider';
    icon: 'quote';
    description: '';
  };
  attributes: {
    subtitle: Attribute.String;
    title: Attribute.String;
    testimonials: Attribute.Relation<
      'blocks.testimonial-slider',
      'oneToMany',
      'api::testimonial.testimonial'
    >;
  };
}

export interface BlocksTestimonialFeatureSlider extends Schema.Component {
  collectionName: 'components_blocks_testimonial_feature_sliders';
  info: {
    displayName: 'Testimonial Feature Slider';
    icon: 'quote';
    description: '';
  };
  attributes: {
    slides: Attribute.Relation<
      'blocks.testimonial-feature-slider',
      'oneToMany',
      'api::testimonial.testimonial'
    >;
  };
}

export interface BlocksStatsBanner extends Schema.Component {
  collectionName: 'components_blocks_stats_banners';
  info: {
    displayName: 'Stats Banner';
    icon: 'earth';
  };
  attributes: {
    stats: Attribute.JSON & Attribute.Required;
  };
}

export interface BlocksSectionIntro extends Schema.Component {
  collectionName: 'components_blocks_section_intros';
  info: {
    displayName: 'section Intro';
    icon: 'filter';
    description: '';
  };
  attributes: {
    subtitle: Attribute.String;
    title: Attribute.String;
    description: Attribute.Text;
    alternateBackground: Attribute.Boolean & Attribute.DefaultTo<false>;
  };
}

export interface BlocksRichContent extends Schema.Component {
  collectionName: 'components_blocks_rich_contents';
  info: {
    displayName: 'Rich Content';
    icon: 'layer';
  };
  attributes: {
    alternateBackground: Attribute.Boolean & Attribute.DefaultTo<false>;
    content: Attribute.Blocks & Attribute.Required;
  };
}

export interface BlocksNewsletterSignupBanner extends Schema.Component {
  collectionName: 'components_blocks_newsletter_signup_banners';
  info: {
    displayName: 'Newsletter Signup Banner';
    icon: 'envelop';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.String;
  };
}

export interface BlocksLogoBanner extends Schema.Component {
  collectionName: 'components_global_logo_banners';
  info: {
    displayName: 'Logo Banner';
    icon: 'picture';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    logos: Attribute.Media<'images', true> & Attribute.Required;
    bottomSpacing: Attribute.Boolean & Attribute.DefaultTo<false>;
  };
}

export interface BlocksLinkCardGrid extends Schema.Component {
  collectionName: 'components_blocks_link_card_grids';
  info: {
    displayName: 'Link Card Grid';
    icon: 'dashboard';
  };
  attributes: {
    columns: Attribute.Integer &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
          max: 4;
        },
        number
      > &
      Attribute.DefaultTo<3>;
    linkCards: Attribute.Component<'components.link-card', true> &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
  };
}

export interface BlocksImageFeature extends Schema.Component {
  collectionName: 'components_blocks_image_features';
  info: {
    displayName: 'Image Feature';
    icon: 'picture';
  };
  attributes: {
    alternateBackground: Attribute.Boolean & Attribute.DefaultTo<false>;
    padded: Attribute.Boolean & Attribute.DefaultTo<false>;
    items: Attribute.Component<'components.2-col-image-with-text', true> &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
  };
}

export interface BlocksHomeConnectivityHero extends Schema.Component {
  collectionName: 'components_blocks_home_connectivity_heroes';
  info: {
    displayName: 'Home Connectivity Hero';
    icon: 'alien';
  };
  attributes: {
    image: Attribute.Media<'images'> & Attribute.Required;
  };
}

export interface BlocksHomeConnectivityFeatures extends Schema.Component {
  collectionName: 'components_blocks_home_connectivity_features';
  info: {
    displayName: 'Home Connectivity Features';
    icon: 'layer';
    description: '';
  };
  attributes: {
    subtitle: Attribute.String;
    title: Attribute.String & Attribute.Required;
    description: Attribute.String;
    features: Attribute.Component<'components.feature-card', true>;
  };
}

export interface BlocksHero extends Schema.Component {
  collectionName: 'components_blocks_heroes';
  info: {
    displayName: 'Hero';
    icon: 'discuss';
    description: '';
  };
  attributes: {
    breadCrumbTrail: Attribute.Component<'components.breadcrumb-link', true>;
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text;
    image: Attribute.Media<'images'>;
    subtitle: Attribute.String;
    CTA: Attribute.Component<'components.button'>;
    alternateBackground: Attribute.Boolean & Attribute.DefaultTo<false>;
  };
}

export interface BlocksFeatureCardGrid extends Schema.Component {
  collectionName: 'components_global_feature_card_grids';
  info: {
    displayName: 'Feature Card Grid';
    icon: 'apps';
    description: '';
  };
  attributes: {
    featureCards: Attribute.Component<'components.feature-card', true>;
    columns: Attribute.Integer &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
          max: 3;
        },
        number
      > &
      Attribute.DefaultTo<3>;
  };
}

export interface BlocksFeasibilitySearch extends Schema.Component {
  collectionName: 'components_global_feasibility_searches';
  info: {
    displayName: 'Feasibility Search';
    icon: 'search';
    description: '';
  };
  attributes: {
    subtitle: Attribute.String;
    title: Attribute.String;
    description: Attribute.Text;
    businessOnly: Attribute.Boolean & Attribute.DefaultTo<false>;
  };
}

export interface BlocksFaQs extends Schema.Component {
  collectionName: 'components_components_fa_qs';
  info: {
    displayName: 'Faqs';
    icon: 'question';
    description: '';
  };
  attributes: {
    imageAlign: Attribute.Enumeration<['left', 'right']> &
      Attribute.DefaultTo<'left'>;
    image: Attribute.Media<'images'>;
    faqs: Attribute.Component<'components.accordion', true>;
    title: Attribute.String;
    description: Attribute.Text;
  };
}

export interface BlocksCtaCardGrid extends Schema.Component {
  collectionName: 'components_global_cta_card_grids';
  info: {
    displayName: 'CTA Card Grid';
    icon: 'calendar';
    description: '';
  };
  attributes: {
    columns: Attribute.Integer &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
          max: 4;
        },
        number
      > &
      Attribute.DefaultTo<3>;
    cards: Attribute.Component<'components.cta-card', true> &
      Attribute.Required &
      Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      >;
  };
}

export interface BlocksCtaBanner extends Schema.Component {
  collectionName: 'components_blocks_cta_banners';
  info: {
    displayName: 'CTA Banner';
    icon: 'brush';
    description: '';
  };
  attributes: {
    inlineStyle: Attribute.Boolean & Attribute.DefaultTo<false>;
    title: Attribute.String & Attribute.Required;
    description: Attribute.Text;
    button: Attribute.Component<'components.button'> & Attribute.Required;
  };
}

export interface BlocksBusinessPackageComparisonTable extends Schema.Component {
  collectionName: 'components_blocks_business_package_comparison_tables';
  info: {
    displayName: 'Business Package Comparison Table';
    icon: 'apps';
    description: '';
  };
  attributes: {
    tableData: Attribute.JSON;
    packages: Attribute.Component<'components.package-card', true>;
  };
}

export interface BlocksBannerLink extends Schema.Component {
  collectionName: 'components_blocks_banner_links';
  info: {
    displayName: 'Banner Link';
    icon: 'picture';
  };
  attributes: {
    url: Attribute.String & Attribute.Required;
    target: Attribute.Enumeration<['_blank', '_self']>;
    bannerImage: Attribute.Media<'images'> & Attribute.Required;
    mobileBannerImage: Attribute.Media<'images'> & Attribute.Required;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface Components {
      'shared.seo': SharedSeo;
      'shared.meta-social': SharedMetaSocial;
      'components.testimonial-card': ComponentsTestimonialCard;
      'components.package-card': ComponentsPackageCard;
      'components.link-card': ComponentsLinkCard;
      'components.home-connectivity-hero': ComponentsHomeConnectivityHero;
      'components.feature-card': ComponentsFeatureCard;
      'components.cta-card': ComponentsCtaCard;
      'components.button': ComponentsButton;
      'components.breadcrumb-link': ComponentsBreadcrumbLink;
      'components.accordion': ComponentsAccordion;
      'components.2-col-image-with-text': Components2ColImageWithText;
      'blocks.testimonial-slider': BlocksTestimonialSlider;
      'blocks.testimonial-feature-slider': BlocksTestimonialFeatureSlider;
      'blocks.stats-banner': BlocksStatsBanner;
      'blocks.section-intro': BlocksSectionIntro;
      'blocks.rich-content': BlocksRichContent;
      'blocks.newsletter-signup-banner': BlocksNewsletterSignupBanner;
      'blocks.logo-banner': BlocksLogoBanner;
      'blocks.link-card-grid': BlocksLinkCardGrid;
      'blocks.image-feature': BlocksImageFeature;
      'blocks.home-connectivity-hero': BlocksHomeConnectivityHero;
      'blocks.home-connectivity-features': BlocksHomeConnectivityFeatures;
      'blocks.hero': BlocksHero;
      'blocks.feature-card-grid': BlocksFeatureCardGrid;
      'blocks.feasibility-search': BlocksFeasibilitySearch;
      'blocks.fa-qs': BlocksFaQs;
      'blocks.cta-card-grid': BlocksCtaCardGrid;
      'blocks.cta-banner': BlocksCtaBanner;
      'blocks.business-package-comparison-table': BlocksBusinessPackageComparisonTable;
      'blocks.banner-link': BlocksBannerLink;
    }
  }
}
