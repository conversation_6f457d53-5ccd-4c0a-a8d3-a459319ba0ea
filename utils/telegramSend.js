"use strict";

const rp = require('request-promise');
const BOT_TOKEN = process.env.BOT_TOKEN;
const TEST_TG_SEND = process.env.TEST_TG_SEND;
const TEST_API_TELEGRAM_URL = process.env.TEST_API_TELEGRAM_URL;
const BASE_URL = "https://api.telegram.org";

if (!BOT_TOKEN) {
    throw new Error("BOT_TOKEN definition missing. Telegram bot cannot start.");
}

module.exports = { send };

async function send(chatId, messageText, botToken = BOT_TOKEN) {
    const baseUrl = TEST_API_TELEGRAM_URL || BASE_URL;

    const options = {
        url: botToken ? `${baseUrl}/bot${botToken}/sendMessage?parse_mode=html` : `${baseUrl}/bot${botToken}/sendMessage`,
        method: "post",
        body: JSON.stringify({chat_id: chatId, text: messageText}),
        headers: {"Content-Type": "application/json"},
        resolveWithFullResponse: true
    }

    let res = await rp(options);
    if (res.statusCode !== 200) {
        let error = new Error(`Telegram Send Error ${res.statusCode}. ${res.body}.`);
        error.statusCode = res.status;

        console.log("fetch result status !== 200", res.statusCode, res.body);
        throw error;
    } else {
        return res.statusCode;
    }
}
