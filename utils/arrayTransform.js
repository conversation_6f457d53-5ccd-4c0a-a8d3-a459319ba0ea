const camelCaseRegex = /^[A-Z][A-Za-z]*$/;

function convertToPascalCase(obj) {
  if (typeof obj !== 'object' || Array.isArray(obj)) {
    return obj;
  }

  const newObj = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (getCamelCaseKeys(key)) {
        const titleCaseKey = key
          .replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^./, str => str.toUpperCase());
        if (obj[key]) {
          if (key !== "id") {
            newObj[titleCaseKey] = convertToPascalCase(obj[key]);
          } else {
            newObj[key] = obj[key];
          }
        } else {
          newObj[titleCaseKey] = null;
        }
      } else {
        newObj[key] = obj[key];
      }
    }
  }

  return newObj;
}

function getCamelCaseKeys(item) {
  return camelCaseRegex.test(item);
}

module.exports = {
  convertToPascalCase
}
