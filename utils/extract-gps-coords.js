const fs = require('fs');
const csvWriter = require('csv-write-stream');
const { Readable } = require('stream');
const readline = require('readline');

const COMMON_REGEX = /\[.*\] info Pre Feasibility Check: \{[^}]*\}/g;

if (!process.argv[2] || !process.argv[3]) {
    throw new Error(
       "Please specify input parameters: <file-input.txt> <file-output.csv> <delimeter: en or ru>"
    )
}

const DELIMITERS = {
    en: ",",
    ru: ";"
}

if (process.argv[4] && !process.argv[4].includes("ru") && !process.argv[4].includes("en")) {
    throw new Error(
       `This delimiters <${process.argv[4]}> is not supported. Supported delimiters: en, ru`
    )
}
class ExtractGpsCoords {
    constructor() {
        this.inputFile =  process.argv[2];
        this.outputFile = process.argv[3];
        this.delimeter = process.argv[4] ? DELIMITERS[process.argv[4]] : DELIMITERS["en"];
        this.coordsCount = 0;
    }
    getKeys() {
        return ["date", "latitude", "longitude"]
    }
    extract() {
        const fileStream = fs.createReadStream(`${this.inputFile}`, "utf8");
        const writer = csvWriter({
            headers: this.getKeys(),
            separator: this.delimeter
        });
        writer.pipe(fs.createWriteStream(`${this.outputFile}`, "utf8"));
        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
        })
        let index = 0;
        let feasibilityCheckLogs = '';
        rl.on("line", (line) => {
            if ( /\[.*\] info Pre Feasibility Check: \{[^}]*\}/ || /\[.*\] info Pre Feasibility Check: /.exec(line)) {
                feasibilityCheckLogs += "\n";
                index = 0;
            }
            if (index < 3) {
                feasibilityCheckLogs += line;
                index++;
            }
        });
        fileStream.on("end", () => {
            if (!feasibilityCheckLogs) {
                throw new Error(
                    "[CSV-Generator]: No matching data found."
                )
            }
            const array = feasibilityCheckLogs.match(COMMON_REGEX);
            array.forEach(line => {
                const date = line.match(/\[.*\]/);
                const latitude = line.match(/latitude: \s*'([^']*)'/);
                const longitude = line.match(/longitude: \s*'([^']*)'/);
                writer.write({
                  date: date ? date[0].replace("[", '').replace("]", "") : "has error",
                  latitude: latitude ? latitude[0].replace(/latitude: '|'/g, '') : "has error",
                  longitude: longitude ? longitude[0].replace(/longitude: '|'/g, '') : "has error",
                })
                this.coordsCount++;
                console.log(`[CSV-Generator]: Processed coords: ${this.coordsCount}`);
            })
            writer.end();
        })
        writer.on("end", () => {
            console.log(`[CSV-Generator]: CSV file has been generated.`);
        })
        fileStream.on("error", (error) => {
            throw new Error(
                `[CSV-Generator]: ${error}`,
            )
        });
    }
}

const parser = new ExtractGpsCoords().extract();
