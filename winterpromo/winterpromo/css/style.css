
body {
    color: #22a4dd;
    font-family: Roboto, sans-serif !important;
    font-size: 12px; /* 24.01px / 16px = 1.500625rem */
    font-style: normal;
    font-weight: normal;
    letter-spacing: 0.1em;
    text-decoration: none;
    background-color: #012138;
}
.text {
    font-size: 16px;
    margin: 80px 0 0 0px;
    font-weight: 500;
    line-height: 37.5px;
    text-transform: uppercase;
}
.text6 {
    font-size: 16px;
    margin: 0px 0 0 0px;
    font-weight: 500;
    line-height: 37.5px;
    text-transform: uppercase;
}
.text7 {
    font-size: 14px;
    margin: 0px 0 0 0px;
    font-weight: 500;

    text-transform: uppercase;
}
.col {

    background: url(../images/graph_element_bg.png) no-repeat;

}
.headline {

    position: relative;
    width: 652px;
}
.rowgroup {
    display: table;
    margin: 5px 0 0 0px;
    position: relative;
}
.text-2 {
    margin: 40px 0 0;
    max-width: 100%;
    height: auto;
    color: #ffffff;
    font-weight: 300;
    text-transform: uppercase;
}
.text-3 {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 5px 0 0 0px;
    position: relative;
}
.from{

    height: auto;
    margin: 5px 0 0 0px;
}
.vector-smart-object_5 {
    max-width: 100%;
    height: auto;
    margin: 5px 0 0 0px;
}
.text-4 {
    font-size: 16px;
    margin: 13px 0px 20px 4px;
    color: #ffffff;
    font-weight: 300;
    text-transform: uppercase;
}
.textterms {
    font-family: Roboto, sans-serif !important;
    font-size: 12px;
    color:white;
    text-align: justify;
    padding-top: 1px;

}
.logo {
    display: block;
    margin: 63px 0 0 6px;
    position: relative;
}
.vector-smart-object_4 {
    margin: 5px 0 0 0px;
    max-width: 100%;
}
.graph-element-bg-2 {

    position: absolute;
    top: 0;
    margin-left: -92px;
}
.iconography {
    max-width: 100%;
    width: 122%;
    height: 100%;
}
