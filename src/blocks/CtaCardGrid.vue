<template>
	<div class="container max-lg:flex max-lg:flex-col max-lg:gap-8 lg:grid lg:grid-cols-1 lg:gap-5 lg:justify-between py-20 md:py-24" :class="colClass">
		<DsCtaCard 
			v-for="card in cards"
			:key="card.id"
			:image="{ url: useFormatImage(card.image.data.attributes.url), alt: card.image.data.attributes.alternativeText }"
			:tags="card.tags"
			url="#"
			:title="card.title"
			@button-clicked="goTo(card.url)"
			@footer-clicked="goTo(card.url)"
		>
			<template #description>
				<div class="text-lg font-light">{{ card.description }}</div>
			</template>
		</DsCtaCard>
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useRouterGoTo } from '@/composables/useRouterGoTo'
import {useFormatImage} from '@/composables/helpers'

interface Card {
	id: number
	image: {
		data: Media
	}
	tags: string[]
	title: string
	description: string
	url: string	
}

interface Props {
	columns: number
	cards: Card[]
}

const props = defineProps<Props>()

const { goTo } = useRouterGoTo()

const colClass = computed(() => {
	var colClass: string;
	
	switch (props.columns) {
		case 1:
			colClass = 'lg:grid-cols-1'
			break;
		case 2:
			colClass = 'lg:grid-cols-2'
			break;
		default:
			colClass = 'lg:grid-cols-3'
			break;
	}
	return colClass
})

</script>