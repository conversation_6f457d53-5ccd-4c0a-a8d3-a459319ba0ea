<template>
	<div class="container">
		<div
			class="p-16 bg-slate-900 rounded-3xl mb-20 md:mb-24 flex gap-8 max-lg:flex-col justify-between items-center bg-center bg-cover bg-[url('../assets/backgrounds/wave-bg-2.svg')]">
			<div class="flex flex-col gap-3 max-lg:text-center">
				<div class="text-h2 font-semibold text-white">{{ title }}</div>
				<p class="text-white">{{ subtitle }}</p>
			</div>
			<div class="flex gap-5 items-center">
				<DsTextInput 
					v-model="v$.email.$model" 
					label="Email Address" 
					name="email"
					:invalid="v$.email.$error"
					:valid="v$.email.$dirty && !v$.email.$invalid"
					invalid-message="Please enter a valid email address"
				/>
				<DsButton label="Subscribe" type="primary" :disabled="isSubmitting" @click="subscribe" />

				<div v-if="errorMessage != ''" class="fixed top-8 inset-x-0 w-fit m-auto">
					<DsToast class="" title="Error" variant="danger">{{ errorMessage }}</DsToast>
				</div>
				<div v-if="successMessage != ''" class="fixed top-8 inset-x-0 w-fit m-auto">
					<DsToast class="" title="Thank you." variant="success">{{ successMessage }}</DsToast>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useVuelidate } from '@vuelidate/core'
import { required, email } from '@vuelidate/validators'

interface Props {
	title: string;
	subtitle: string;
}

interface FormData {
	email: string;
}

defineProps<Props>()

const formData = reactive<FormData>({
	email: '',
});

const rules = {
	email: { required, email },
}

// Initialize Vuelidate
const v$ = useVuelidate(rules, formData)

const successMessage = ref<string>('')
const errorMessage = ref<string>('')
const isSubmitting = ref<boolean>(false)

const subscribe = async () => {
	const isFormCorrect = await v$.value.$validate()

	if (isFormCorrect) {
		isSubmitting.value = true
		const API_KEY = import.meta.env.VITE_BREVO_API_KEY
		const LIST_ID = 2

		try {
			const response = await fetch('https://api.brevo.com/v3/contacts/doubleOptinConfirmation', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'api-key': API_KEY,
				},
				body: JSON.stringify({
					email: formData.email,
					includeListIds: [LIST_ID],
					templateId: 1,
					redirectionUrl: import.meta.env.VITE_TLD
				}),
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`)
			}

			successMessage.value = 'Thank you! Please complete your sign up via the confirmation email.'
			errorMessage.value = ''
			formData.email = ''
			v$.value.$reset()
		} catch (err) {
			errorMessage.value = 'There was an issue signing you up. Please try again.';
			successMessage.value = '';
		} finally {
			isSubmitting.value = false;
		}
	}
}
</script>