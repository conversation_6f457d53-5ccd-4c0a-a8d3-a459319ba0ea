<template>
	<DsFeatureList :feature-items="featureItems" />
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
	level?: number,
	children: Child[]
}

const props = defineProps<Props>()

const featureItems = computed(() => {
	const items: (string | undefined)[] = [] // Initialize the array with correct type
	const extractText = (child: Child) => {
		if (child.text) {
			items.push(child.text)
		}
		if (child.children) {
			child.children.forEach(extractText)
		}
	}
	props.children.forEach(extractText)
	return items
})

</script>