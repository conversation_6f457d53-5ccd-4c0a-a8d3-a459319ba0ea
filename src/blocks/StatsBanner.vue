<template>
	<div class="c-stats-banner mb-20 md:mb-24">
		<div class="container">
			<div class="py-20 px-8 bg-blue rounded-3xl grid grid-cols-1 gap-8 items-center" :class="statsGrid">
				<div class="text-white text-center" v-for="stat in stats" :key="stat.label">
					<div class="text-display font-semibold">{{ stat.value }}</div>
					<div class="text-lg font-semibold">{{ stat.label }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import {computed} from 'vue'
interface Stat {
	label: string
	value: string
}
interface Props {
	stats: Stat[]
}

const props = defineProps<Props>()

const statsGrid = computed(() => {
	switch (props.stats.length) {
		case 1:
			return ''
		case 2:
			return 'md:grid-cols-2'
		case 3:
			return 'md:grid-cols-3'
		default:
			return 'md:grid-cols-4'
	}
})
</script>