<template>
	<div class="py-20 md:py-24 bg-slate-50" :class="{ 'mb-20 md:mb-24': bottomSpacing }">
		<div class="container flex flex-col gap-8">
			<div v-if="title" class="text-center text-slate-600 text-base font-medium">{{ title }}</div>
			<div class="flex max-md:flex-col items-center gap-8 justify-between flex-wrap">
				<img v-for="logo in logos.data" :key="logo.id" :src="useFormatImage(logo.attributes.url)" :alt="logo.attributes.alternativeText">
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import {useFormatImage} from '@/composables/helpers'

interface Props {
	title?: string
	logos: {
		data: Media[]
	}
	bottomSpacing?: boolean
}

withDefaults(defineProps<Props>(), {
	bottomSpacing: false
})
</script>