<template>
	<div class="bg-slate-50 py-20 md:py-24 mb-20 md:mb-24">
		<Splide ref="testimonialslider" :options="splidOptions" :aria-label="subtitle" :has-track="false">
			<div class="container">
				<div class="flex gap-10 justify-between items-end mb-12">
					<div>
						<div class="text-subtitle text-blue font-semibold">{{ subtitle }}</div>
						<div class="text-h1 text-stone-800 font-semibold">{{ title }}</div>
					</div>
					<div class="items-center gap-4 hidden md:flex">
						<a
							@click.prevent="sliderPrev"
							href="#"
							class="
								rounded-full
								w-12 
								h-12 
								bg-[rgba(255,_255,_255,_0.75)]
								backdrop-blur-sm 
								flex
								flex-col
								items-center
								justify-center
								shadow-[0px_1px_3px_0px_rgba(0,_0,_0,_0.10),_0px_1px_2px_0px_rgba(0,_0,_0,_0.06)]
							" 
						><span class="material-symbols-outlined ms-2">arrow_back_ios</span></a>
						<a 
							@click.prevent="sliderNext"
							href="#"
							class="
								rounded-full
								w-12 
								h-12 
								bg-[rgba(255,_255,_255,_0.75)]
								backdrop-blur-sm 
								flex
								flex-col
								items-center
								justify-center
								shadow-[0px_1px_3px_0px_rgba(0,_0,_0,_0.10),_0px_1px_2px_0px_rgba(0,_0,_0,_0.06)]
								text-center
							" 
						><span class="material-symbols-outlined ms-2">arrow_forward_ios</span></a>
					</div>
				</div>
				<div class="md:mr-[calc(50%_-_(50vw_-_8px))]">
					<SplideTrack>
						<SplideSlide v-for="testimonial in testimonials.data" :key="testimonial.id">
							<DsTestimonialCard 
								:author="testimonial.attributes.testimonial.author"
								:location=" testimonial.attributes.testimonial.location"
								:testimonial=" testimonial.attributes.testimonial.testimonial"
								:rating=" testimonial.attributes.testimonial.rating"
							/>
						</SplideSlide>
					</SplideTrack>
				</div>
				<div class="items-center gap-4 flex justify-end md:hidden my-8">
					<a
						@click.prevent="sliderPrev"
						href="#"
						class="
							rounded-full
							w-12 
							h-12 
							p-3
							bg-[rgba(255,_255,_255,_0.75)]
							backdrop-blur-sm 
							material-symbols-outlined
							shadow-[0px_1px_3px_0px_rgba(0,_0,_0,_0.10),_0px_1px_2px_0px_rgba(0,_0,_0,_0.06)]
						" 
					>arrow_back_ios</a>
					<a 
						@click.prevent="sliderNext"
						href="#"
						class="
							rounded-full
							w-12 
							h-12 
							p-3
							bg-[rgba(255,_255,_255,_0.75)]
							backdrop-blur-sm 
							material-symbols-outlined
							shadow-[0px_1px_3px_0px_rgba(0,_0,_0,_0.10),_0px_1px_2px_0px_rgba(0,_0,_0,_0.06)]
						" 
					>arrow_forward_ios</a>
				</div>
			</div>
		</Splide>
	</div>
</template>

<script lang="ts" setup>
import {ref} from 'vue';
import { Splide, SplideSlide, SplideTrack } from '@splidejs/vue-splide';
import '@splidejs/vue-splide/css';

interface Props {
	subtitle?: string
	title: string
	testimonials: {
		data: Testimonial[]
	}
}

defineProps<Props>()

const splidOptions = ref({
	type: 'slide',
	autoHeight: true,
	perPage: 1,
	perMove: 1,
	gap: "32px",
	pagination: false,
	arrows: false,
	mediaQuery: 'min',
	breakpoints: {
		768: {
			padding: { left: 0, right: 200 },
		},
		1024: {
			perPage: 2,
		},
		1512: {
			perPage: 3,
		}
  }
})

const testimonialslider = ref<InstanceType<typeof Splide>>()

const sliderPrev = () => {
	if (testimonialslider.value && 'go' in testimonialslider.value) {
		testimonialslider.value.go( '<' );
	}
}
const sliderNext = () => {
	if (testimonialslider.value && 'go' in testimonialslider.value) {
		testimonialslider.value.go( '>' );
	}
}

</script>