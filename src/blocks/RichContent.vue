<template>
	<div class="pb-20 md:pb-24" :class="{ 'bg-slate-50 pt-20 md:pt-24' : alternateBackground }">
		<div class="container">
			<component v-bind="block" v-for="block in content" :is="useGetComponent(block.type)" :key="block.id"  />
		</div>
	</div>
</template>

<script lang="ts" setup>
import {useGetComponent} from '@/composables/getRTEComponent'

interface Props {
	content: RTEContent[],
	alternateBackground?: boolean
}

defineProps<Props>()
</script>