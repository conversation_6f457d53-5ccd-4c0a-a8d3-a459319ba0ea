<template>
	<div class="py-20 md:py-24">
		<div class="container grid grid-cols-1 lg:grid-cols-3 gap-10 justify-between">
			<div class="">
				<div v-if="subtitle" class="text-subtitle text-blue font-semibold mb-2 uppercase">{{ subtitle }}</div>
				<div class="text-h1 text-digital-black font-semibold">{{ title }}</div>
				<p v-if="description" class="text-slate-500 font-light mt-8">{{ description }}</p>
			</div>
			<div class="md:col-span-2 lg:flex lg:justify-end">
				<div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-[800px]">
					<DsFeatureCard
						v-for="card in features"
						:key="card.id"
						:textAlign="card.textAlign"
						:title="card.title"
						:description="card.description"
						:icon="card.icon"
						:iconStyle="card.iconStyle"
						:linkText="card.linkText"
						:linkUrl="card.linkUrl"
						class="col-span-1 bg-slate-50 rounded-3xl p-8"
						@button-clicked="goTo(card.linkUrl)"
					></DsFeatureCard>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { useRouterGoTo } from '@/composables/useRouterGoTo'

interface Props {
	subtitle?: string
	title: string
	description?: string
	features: featureCard[]
}

defineProps<Props>()

const { goTo } = useRouterGoTo()
</script>