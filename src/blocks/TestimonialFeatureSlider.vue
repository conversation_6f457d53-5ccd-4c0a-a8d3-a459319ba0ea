<template>
	<div class="c-testimonial-feature-slider bg-slate-50 bg-center bg-cover bg-[url('../assets/backgrounds/wave-bg-3.svg')] py-20 md:py-24 mb-20 md:mb-24">
		<div class="container grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
			<div class="order-1">
				<div class="max-w-[510px]">
					<Splide ref="testimonialslider" :options="splidOptions">
						<SplideSlide v-for="slide in slides.data" :key="slide.id">
							<div class="text-h1 text-digital-black font-semibold line-clamp-4">{{  slide.attributes.testimonial.testimonial }}</div>
						</SplideSlide>
					</Splide>
					<div class="flex gap-10 justify-between items-end mt-20">
						<div class="items-center gap-4 flex">
							<a
								@click.prevent="sliderPrev"
								href="#"
								class="
									rounded-full
									w-12
									h-12
									p-3
									bg-[rgba(255,_255,_255,_0.75)]
									backdrop-blur-sm
									material-symbols-outlined
									shadow-[0px_1px_3px_0px_rgba(0,_0,_0,_0.10),_0px_1px_2px_0px_rgba(0,_0,_0,_0.06)]
								"
							>arrow_back_ios</a>
							<a
								@click.prevent="sliderNext"
								href="#"
								class="
									rounded-full
									w-12
									h-12
									p-3
									bg-[rgba(255,_255,_255,_0.75)]
									backdrop-blur-sm
									material-symbols-outlined
									shadow-[0px_1px_3px_0px_rgba(0,_0,_0,_0.10),_0px_1px_2px_0px_rgba(0,_0,_0,_0.06)]
								"
							>arrow_forward_ios</a>
						</div>
					</div>
				</div>
			</div>
			<div class="order-0 lg:order-2">
				<Splide ref="testimonialImageslider" :options="splidImagesOptions">
					<SplideSlide v-for="slide in slides.data" :key="slide.id">
						<img
							:key="slide.id"
							:src="useFormatImage(slide.attributes.image.data.attributes.url)"
							:alt="slide.attributes.image.data.attributes.alternativeText"
							class="rounded-3xl"
						>
					</SplideSlide>
				</Splide>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import {ref, onMounted} from 'vue';
import { Splide, SplideSlide } from '@splidejs/vue-splide';
import '@splidejs/vue-splide/css';
import {useFormatImage} from '@/composables/helpers'
interface Props {
	slides: {
		data: Testimonial[]
	}
}

defineProps<Props>()

const testimonialslider = ref<InstanceType<typeof Splide>>()
const testimonialImageslider = ref<InstanceType<typeof Splide>>()

const splidOptions = ref({
	type: 'loop',
	perPage: 1,
	perMove: 1,
	pagination: false,
	arrows: false,
})

const splidImagesOptions = ref({
	type: 'fade',
	perPage: 1,
	perMove: 1,
	pagination: false,
	arrows: false,
})

const formatImage = (imageUrl: string) => { 
	return import.meta.env.VITE_BACKEND_BASE_URL + imageUrl
}

const sliderPrev = () => {
	if (testimonialslider.value && 'go' in testimonialslider.value) {
		testimonialslider.value.go( '<' );
		// testimonialImageslider.value.go( '<' );
	}
}
const sliderNext = () => {
	if (testimonialslider.value && 'go' in testimonialslider.value) {
		testimonialslider.value.go( '>' );
		// testimonialImageslider.value.go( '>' );
	}
}


onMounted(() => {
	const imageSplide = testimonialImageslider.value?.splide;
	testimonialslider.value?.sync( imageSplide );
})
</script>
