<template>
	<div class="container flex max-lg:flex-col justify-between gap-12 lg:!gap-16 mt-10 md:!mt-20 mb-10 md:mb-24">
		<div class="order-1 max-w-[800px] align-self-center">
			<DsBreadcrumbs
				v-if="breadCrumbTrail"
				:breadcrumbs="breadCrumbTrail"
				class="mb-8 lg:mb-20 block"
			/>
			<div v-if="subtitle" class="text-subtitle text-blue font-semibold mb-6 uppercase">{{ subtitle }}</div>
			<h1 class="text-display text-stone-800 font-semibold">{{ title }}</h1>
			<p v-if="description" class="text-slate-500 font-light mt-8 lg:mt-11 whitespace-break-spaces" v-html="description"></p>
			<router-link v-if="CTA" :to="CTA.url">
				<DsButton
					class="mt-6"
					:label="CTA.label" 
					:type="CTA.type" 
					:icon-before="CTA.iconBefore"
					:icon-after="CTA.iconAfter"
					:href="CTA.url"
				/>
			</router-link>
		</div>
		<div class="order-0 lg:order-2 shrink-0">
			<img v-if="image && image.data" class="w-auto h-full object-cover rounded-3xl lg:max-w-[620px]" :src="useFormatImage(image.data.attributes.url)" :alt="image.data.attributes.alternativeText">
		</div>
		
	</div>
</template>

<script lang="ts" setup>
import {useFormatImage} from '@/composables/helpers'

interface Props {
	breadCrumbTrail?: Breadcrumb[]
	title: string
	subtitle?: string
	description?: string
	image?: {
		data: Media
	}
	CTA?: Button
}

defineProps<Props>()

</script>