<template>
	<div class="bg-blue pt-[200px] pb-20 bg-center bg-no-repeat bg-cover bg-[url('../assets/backgrounds/wave-bg-4-home-connectivity-hero.svg')] -mt-[177px]">
		<div class="container">
			<h1 id="page-heading" class="text-h1 text-white font-semibold mb-11">CYBERSMART Fibre for your Home (FTTH)</h1>
			<div class="c-card-slider max-lg:mr-[calc(50%_-_(50vw_-_8px))]">
				<Splide ref="heroslider" :options="splidOptions" aria-labelledby="page-heading" :has-track="false">
					<SplideTrack>
						<SplideSlide>
							<div class="px-8 pt-10 pb-8 bg-white rounded-3xl flex flex-col justify-between gap-10 h-full">
								<div>
									<div class="text-h1 text-blue font-semibold mb-8">Find your best connectivity option:</div>
									<p class="text-slate-500 font-light">Your address is key to us providing the best fibre option for your home. </p>
								</div>
								<div class="flex items-center gap-2">
									<div class="material-symbols-outlined text-xl text-blue">search</div>
									<router-link to="/feasibility-search">
										<DsButton
											label="Search My Address"
											type="ghost"
											icon-after="arrow_right_alt"
											class="ps-0"
											href="/feasibility-search"
										/>
									</router-link>
								</div>
							</div>
						</SplideSlide>
						<SplideSlide>
							<div class="px-8 pt-10 pb-8 bg-white rounded-3xl flex flex-col justify-between gap-10 h-full">
								<div>
									<div class="text-h1 text-blue font-semibold mb-8">Need some advice from our team?</div>
									<p class="text-slate-500 font-light">Our team of experts are waiting to help in any way they can to get you connected.</p>
								</div>
								<div class="flex items-center gap-2">
									<router-link to="/contact-us">
										<DsButton
											label="Contact Us"
											type="ghost"
											icon-after="arrow_right_alt"
											class="ps-0"
											href="/contact-us"
										/>
									</router-link>
								</div>
							</div>
						</SplideSlide>
						<SplideSlide>
							<div class="bg-white rounded-3xl flex flex-col gap-10 overflow-hidden h-full">
								<img class="w-full h-full object-cover" :src="useFormatImage(image.data.attributes.url)" :alt="image.data.attributes.alternativeText" />
							</div>
						</SplideSlide>
					</SplideTrack>
					<div class="container items-center justify-end gap-4 flex md:hidden mt-10">
						<a
							@click.prevent="sliderPrev"
							href="#"
							class="
								rounded-full
								w-12 
								h-12 
								bg-[rgba(255,_255,_255,_0.75)]
								backdrop-blur-sm 
								flex
								flex-col
								items-center
								justify-center
								shadow-[0px_1px_3px_0px_rgba(0,_0,_0,_0.10),_0px_1px_2px_0px_rgba(0,_0,_0,_0.06)]
							" 
						><span class="material-symbols-outlined ms-2">arrow_back_ios</span></a>
						<a 
							@click.prevent="sliderNext"
							href="#"
							class="
								rounded-full
								w-12 
								h-12 
								bg-[rgba(255,_255,_255,_0.75)]
								backdrop-blur-sm 
								flex
								flex-col
								items-center
								justify-center
								shadow-[0px_1px_3px_0px_rgba(0,_0,_0,_0.10),_0px_1px_2px_0px_rgba(0,_0,_0,_0.06)]
								text-center
							" 
						><span class="material-symbols-outlined ms-2">arrow_forward_ios</span></a>
					</div>
				</Splide>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import {ref} from 'vue';
import { Splide, SplideSlide, SplideTrack } from '@splidejs/vue-splide';
import '@splidejs/vue-splide/css';
import { useFormatImage } from '@/composables/helpers'
interface Props {
	image: {
		data: Media
	}
}

defineProps<Props>()

const splidOptions = ref({
	type: 'slide',
	autoHeight: false,
	perPage: 1,
	perMove: 1,
	gap: "32px",
	pagination: false,
	arrows: false,
	mediaQuery: 'min',
	padding: { left: 0, right: 100 },
	breakpoints: {
		1024: {
			destroy: true,
		},
  }
})

const heroslider = ref<InstanceType<typeof Splide>>()

const sliderPrev = () => {
	if (heroslider.value && 'go' in heroslider.value) {
		heroslider.value.go( '<' );
	}
}
const sliderNext = () => {
	if (heroslider.value && 'go' in heroslider.value) {
		heroslider.value.go( '>' );
	}
}
</script>

<style scoped>
:deep(.c-card-slider .splide.is-initialized:not(.is-active) .splide__list) {
	@apply md:grid md:grid-cols-3 md:gap-5
}
</style>