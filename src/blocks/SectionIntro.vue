<template>
	<div class="pb-16" :class="{ 'bg-slate-50 pt-24 ' : alternateBackground }">
		<div class="container max-w-[620px]">
			<DsSectionIntro
				:subtitle="subtitle"
				:title="title"
				:description="description"
			></DsSectionIntro>
		</div>
	</div>
</template>

<script lang="ts" setup>
interface Props {
	subtitle?: string
	title?: string
	description?: string
	alternateBackground?: boolean
}

defineProps<Props>()
</script>