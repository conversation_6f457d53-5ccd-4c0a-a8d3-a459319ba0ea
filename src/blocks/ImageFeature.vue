<template>
	<div class="mb-20 md:mb-24" :class="{ 'bg-slate-50 py-20 md:py-24' : alternateBackground, 'py-20' : padded }">
		<div class="container flex flex-col gap-8">
			<div v-for="imageFeature in items" :key="imageFeature.id" class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:!gap-20 items-center">
				<div class="flex flex-col gap-6 order-1">
					<div v-if="imageFeature.icon" class="w-12 h-12 p-2 bg-white rounded-xl border border-slate-300 justify-center items-center gap-2 inline-flex material-symbols-outlined">{{ imageFeature.icon }}</div>
					<component v-bind="block" v-for="block in imageFeature.content" :is="RteComponent(block.type)" :key="block.id"  />
					<div v-if="imageFeature.button" class="flex gap-4">
						<router-link v-for="buttonItem in imageFeature.button" :key="buttonItem.id" :to="buttonItem.url ?? '#'">
							<DsButton
								:label="buttonItem.label"
								:type="buttonItem.type"
								:href="buttonItem.url"
								:icon-before="buttonItem.iconBefore"
								:icon-after="buttonItem.iconAfter"
							/>
						</router-link>
					</div>
				</div>
				<div class="h-full order-0" :class="imageFeature.imageAlign == 'left' ? 'lg:order-0' : 'lg:order-2'">
					<img class="w-full h-full object-cover rounded-3xl" :src="useFormatImage(imageFeature.image.data.attributes.url)" :alt="imageFeature.image.data.attributes.alternativeText">
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { useGetComponent } from '@/composables/getRTEComponent'
import {useFormatImage} from '@/composables/helpers'

interface ImageFeature {
	id: number
	content: RTEContent[]
	image: {
		data: Media
	}
	imageAlign: string
	icon?: string
	button?: Button[]
}
interface Props {
	alternateBackground?: boolean
	items: ImageFeature[]
	padded?: boolean
}

withDefaults(defineProps<Props>(), {
	padded: false
})

const RteComponent = (type: string) => {
	if (type === 'list') {
		return	useGetComponent(type, '2col') //This component has a special context, since it splits over 2 cols
	}
	return	useGetComponent(type)
}
</script>