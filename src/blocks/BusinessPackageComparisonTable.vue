<template>
	<div class="c-business-package-comparison-table container mb-20 md:mb-24">
		<div class="table-wrapper overflow-auto" tabindex="0" role="group" aria-labelledby="content">
			<table class="text-center w-full h-[1px] border-collapse min-w-[1000px] table-auto border-spacing-0">
				<tbody>
					<tr>
						<th class="w-[150px] bg-white z-10 px-8"></th>
						<td v-for="hostingPackage in packages" :key="hostingPackage.id" class="px-4 align-top">
							<DsPackageCard
								:title="hostingPackage.title"
								:description="hostingPackage.description"
								:cost="hostingPackage.cost"
								:ctaLabel="hostingPackage.ctaLabel"
								ctaUrl="#"
								:ribbonText="hostingPackage.ribbonText"
								:ribbonIcon="hostingPackage.ribbonIcon"
								@button-clicked="goTo(hostingPackage.ctaUrl)"
							/>
						</td>
					</tr>
					<tr v-for="(row, heading) in tableData" :key="heading">
						<th class="w-[150px] py-8 text-left text-zinc-900 text-lg font-semibold whitespace-nowrap bg-white relative after:absolute after:h-[1px] after:w-full after:bottom-0 after:left-0 after:bg-slate-300 z-10 px-8">{{ heading }}</th>
						<td v-for="cell in row" :key="cell" class="relative py-8 align-top after:absolute after:h-[1px] after:w-full after:bottom-0 after:left-0 after:bg-slate-300">
							<div v-if="typeof cell === 'boolean'" class="material-symbols-outlined filled text-blue">{{ cell ? 'check_circle' : '' }}</div>
							<div v-else class="text-zinc-900 text-lg font-light">{{ cell }}</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { useRouterGoTo } from '@/composables/useRouterGoTo'
interface Props {
	packages: Package[]
	tableData: object
}

defineProps<Props>()

const { goTo } = useRouterGoTo()
</script>

<style scoped>

::-webkit-scrollbar {
  height: 16px;
  z-index: 100;
  position: relative;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
}

::-webkit-scrollbar-track {
  background-color: #e2e8f0;
}

</style>