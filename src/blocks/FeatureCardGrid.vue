<template>
	<div class="bg-slate-50 mb-20 md:mb-24">
		<div class="container flex flex-col gap-12 pb-20 md:pb-24">
			<div class="max-lg:flex max-lg:flex-col max-lg:gap-8 lg:grid lg:grid-cols-1 lg:gap-16 lg:justify-between" :class="colClass">
				<DsFeatureCard
					v-for="card in featureCards"
					:key="card.id"
					:textAlign="card.textAlign"
					:title="card.title"
					:description="card.description"
					:icon="card.icon"
					:iconStyle="card.iconStyle"
					:linkText="card.linkText"
					:linkUrl="card.linkUrl"
					class="col-span-1"
					@button-clicked="goTo(card.linkUrl)"
				></DsFeatureCard>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useRouterGoTo } from '@/composables/useRouterGoTo'

interface Props {
	columns: number
	featureCards: featureCard[]
}

const props = defineProps<Props>()

const colClass = computed(() => {
	var colClass: string;
	
	switch (props.columns) {
		case 1:
			colClass = 'lg:grid-cols-1'
			break;
		case 2:
			colClass = 'lg:grid-cols-2'
			break;
		default:
			colClass = 'lg:grid-cols-3'
			break;
	}
	return colClass
})

const { goTo } = useRouterGoTo()
</script>