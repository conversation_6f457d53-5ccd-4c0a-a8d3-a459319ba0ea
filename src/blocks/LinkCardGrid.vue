<template>
	<div class="container max-lg:flex max-lg:flex-col max-lg:gap-8 lg:grid lg:grid-cols-1 lg:gap-5 lg:justify-between mb-20 md:mb-24" :class="colClass">
		<DsLinkCard 
			v-for="card in linkCards"
			:key="card.id"
			url="#"
			:features="card.featureList"
			class="col-span-1"
			@button-clicked="goTo(card.url)"
		>
			<template #title><div class="text-5xl font-semibold">{{ card.title }}</div></template>
			<div class="text-lg font-light">{{ card.descripion }}</div>
		</DsLinkCard>
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useRouterGoTo } from '@/composables/useRouterGoTo'

interface linkCard {
	id: number,
	title: string,
	descripion: string,
	url: string,
	featureList: string[],
	urlTarget: string
}

interface Props {
	columns: number
	linkCards: linkCard[]
}

const props = defineProps<Props>()

const { goTo } = useRouterGoTo()

const colClass = computed(() => {
	var colClass: string;
	
	switch (props.columns) {
		case 1:
			colClass = 'lg:grid-cols-1'
			break;
		case 2:
			colClass = 'lg:grid-cols-2'
			break;
		default:
			colClass = 'lg:grid-cols-3'
			break;
	}
	return colClass
})
</script>