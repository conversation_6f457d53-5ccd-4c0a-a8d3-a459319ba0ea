<template>
	<div class="container">
		<div class="p-16 bg-slate-900 rounded-3xl mb-20 md:mb-24 flex gap-8 max-lg:flex-col items-center bg-center bg-cover bg-[url('../assets/backgrounds/wave-bg-2.svg')]" :class="bannerStyleClass">
			<div class="flex flex-col gap-8 max-lg:text-center" :class="!inlineStyle ? 'text-center max-w-[800px] mx-auto' : ''">
				<div class="text-h1 font-semibold text-white">{{ title }}</div>
				<p class="text-white">{{ description }}</p>
			</div>
			<div class="max-lg:text-center" :class="!inlineStyle ? 'text-center' : 'text-end'">
				<router-link :to="button.url">
					<DsButton
						:label="button.label"
						:type="button.type"
						:icon-before="button.iconBefore"
						:icon-after="button.iconAfter"
						:href="button.url"
					/>
				</router-link>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

interface Props {
	title: string;
	description?: string;
	inlineStyle?: boolean;
	button: Button
}

const props = defineProps<Props>()

const bannerStyleClass = computed(() => {
	return props.inlineStyle ? 'justify-between' : 'flex-col'
})
</script>