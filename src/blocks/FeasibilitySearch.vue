<template>
	<div id="feasibility-search" class="bg-slate-900 py-12 bg-center bg-cover bg-[url('../assets/backgrounds/wave-bg-1.svg')]">
		<div class="container">
			<div v-if="subtitle" class="text-teal text-subtitle text-center uppercase mb-1 font-semibold">{{ subtitle }}
			</div>
			<div v-if="title" class="text-h1 text-white text-center mb-12">{{ title }}</div>
			<p v-if="description" class="text-white font-light mb-12 text-center max-w-[950px] mx-auto">{{ description
				}}</p>
			<div
				class="py-[120px] bg-center bg-cover bg-[url('../assets/feasibility-search-bg2.jpg')] rounded-xl shadow-inner">
				<div class="opacity-75 bg-white rounded-3xl shadow px-4 py-10 md:p-[70px] mx-auto max-w-[794px]">
					<div class="relative">
						<div class="material-symbols-outlined absolute left-4 top-[10px] text-stone-800 text-base" aria-hidden="true">search</div>
						<svg class="absolute left-9 top-[12px] w-6" xmlns="http://www.w3.org/2000/svg" width="34" height="18" viewBox="0 0 34 18" fill="none" aria-hidden="true">
							<path d="M33.8927 16.1469L28.7031 0.873814C28.506 0.359168 27.9782 0 27.4202 0C26.8611 0 26.3337 0.359169 26.1304 0.891982L22.2108 12.4266L18.2853 0.873814C18.0882 0.359168 17.5604 0 17.0024 0C16.4433 0 15.9159 0.359169 15.7126 0.891982L11.7909 12.4329L7.86312 0.873814C7.66598 0.359168 7.13824 0 6.58019 0C6.02112 0 5.4937 0.359169 5.29043 0.891982L0.107596 16.1437C0.057545 16.2831 0 16.47 0 16.6797C0 17.42 0.600615 18 1.36806 18C2.01839 18 2.47974 17.5779 2.64794 17.1594L6.58359 5.18244L10.5111 17.1573C10.5186 17.1768 10.5288 17.1985 10.5383 17.2167L10.5451 17.231C10.5679 17.2785 10.5962 17.3281 10.6231 17.3683C10.6919 17.4777 10.7763 17.5758 10.8917 17.6737L10.9339 17.7093C10.9864 17.7498 11.0409 17.7869 11.0878 17.8127C11.1066 17.8239 11.1273 17.8347 11.1655 17.8533C11.208 17.8742 11.2506 17.8945 11.2949 17.9113C11.3391 17.9277 11.3871 17.9406 11.4409 17.9542C11.4699 17.9626 11.4981 17.9703 11.5295 17.9762C11.5911 17.9874 11.6571 17.9934 11.7252 17.9962C11.7463 17.9986 11.7685 18 11.7923 18L11.8587 17.9962C11.9265 17.993 11.9915 17.9874 12.0531 17.9762C12.0831 17.9703 12.1117 17.963 12.1505 17.9518C12.1975 17.9403 12.2448 17.9273 12.284 17.9127C12.3289 17.8962 12.3718 17.876 12.4338 17.8449L12.4848 17.8187C12.5424 17.7862 12.5958 17.7498 12.6476 17.71L12.6834 17.681C12.792 17.5916 12.8839 17.4874 12.9517 17.3795C12.986 17.3278 13.0143 17.2782 13.0429 17.2191L17.0061 5.18174L20.8965 17.0465L20.9414 17.1765C21.0848 17.5213 21.3721 17.7907 21.7191 17.9116L21.7627 17.9266C21.8257 17.9472 21.8904 17.9651 21.9571 17.9766C21.9884 17.9825 22.0218 17.9853 22.0769 17.9899C22.1076 17.9937 22.1379 17.9965 22.1692 17.9983L22.2148 17.9993C22.2179 17.9993 22.221 17.9993 22.224 17.9993L22.2588 17.9979C22.2901 17.9962 22.3241 17.9923 22.3677 17.9874C22.4018 17.9853 22.4355 17.9818 22.4637 17.9769C22.5318 17.9651 22.5962 17.9476 22.6592 17.927L22.6895 17.9172C23.0507 17.7907 23.3384 17.5217 23.4814 17.1765L27.4239 5.18174L31.3398 17.1258L31.3521 17.1587C31.5206 17.5772 31.9816 17.9993 32.6319 17.9993C33.399 17.9993 34 17.4193 34 16.679C33.9997 16.4721 33.9428 16.2863 33.8927 16.1469Z" fill="#209FD6"/>
						</svg>
						<GMapAutocomplete
							class="ps-[68px] pe-4 pb-2 pt-[9px] bg-white rounded-xl border transition-all focus:border-sky-500 focus-visible:outline-none w-full"
							placeholder="Enter your address to get started" :onClass="() => {}" :onPlaceholder="() => {}"
							:options="autoCompleteOptions" @place_changed="searchLocation" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import brand from "@/brand";
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';


interface Props {
	subtitle?: string;
	title?: string;
	description?: string;
	businessOnly?: string | boolean;
}

const props = withDefaults(defineProps<Props>(), {
	businessOnly: 'false'
})

interface Location {
	formatted_address: string;
}

interface Center {
	lat: number;
	lng: number;
}

interface Bounds {
	north: number;
	south: number;
	east: number;
	west: number;
}

interface AutoCompleteOptions {
	bounds?: Bounds;
	componentRestrictions?: { country: string };
}

const router = useRouter();
const store = useStore();

const autoCompleteOptions = ref<AutoCompleteOptions>({});

const upingtonCenter: Center = { lat: -28.435064, lng: 21.233283 };
const upingtonBounds: Bounds = {
	north: upingtonCenter.lat + 0.2,
	south: upingtonCenter.lat - 0.2,
	east: upingtonCenter.lng + 0.2,
	west: upingtonCenter.lng - 0.2,
};

const capeTownCenter: Center = { lat: -33.925278, lng: 18.423889 };
const capeTownBounds: Bounds = {
	north: capeTownCenter.lat + 0.2,
	south: capeTownCenter.lat - 0.2,
	east: capeTownCenter.lng + 0.4,
	west: capeTownCenter.lng - 0.2,
};

const searchBounds: Record<string, AutoCompleteOptions> = {
	"cybersmart": {},
	"ftta": { bounds: capeTownBounds },
	"gigalight": { bounds: upingtonBounds },
};

const searchLocation = (location: Location): void => {
	store.commit('setSelectedProduct', null);
	router.push({
		name: 'location',
		params: {
			address: location.formatted_address,
			forbusiness: props.businessOnly
		},
	});
};



onMounted(() => {
	store.commit('setFreshSearch');

	const currentBrand: string = brand.getBrand();

	autoCompleteOptions.value = {
		...searchBounds[currentBrand],
		componentRestrictions: { country: "za" },
	};
});
</script>


<style>
.pac-container {
	@apply p-0 after:content-[none] rounded-xl
}

.pac-item {
	@apply px-4 py-2 font-roboto text-base text-digital-black hover:text-white hover:bg-blue transition-all font-light
}

.pac-item:hover .pac-item-query {
	@apply text-white
}

.pac-item-query {
	@apply text-digital-black transition-all font-roboto text-base
}

.pac-matched {
	@apply text-digital-black font-semibold
}
</style>