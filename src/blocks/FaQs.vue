<template>
	<div class="c-faqs mb-20 md:mb-24">
		<div class="container grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-20 items-center"></div>
		<div class="container" :class="{ 'grid grid-cols-1 lg:grid-cols-2 gap-8 lg:!gap-20 items-center' : image }">
			<div class="h-full order-0" :class="imageAlign == 'left' ? 'lg:order-0' : 'lg:order-2'">
				<img class="w-full h-auto object-cover rounded-3xl" :src="useFormatImage(image.data.attributes.url)" :alt="image.data.attributes.alternativeText">
			</div>
			<div class="order-1">
				<div v-if="title" class="text-h1 text-digital-black font-semibold mb-9">{{ title }}</div>
				<p v-if="description" class="text-slate-500 font-light mb-8">{{ description }}</p>
				<DsAccordion v-for="faq in faqs" :label="faq.label" :open="false">
					<RTEContent :content="faq.content" />
				</DsAccordion>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import RTEContent from './RTEContent.vue';
import { useFormatImage } from '@/composables/helpers'

interface Faq {
	label: string
	content: RTEContent[]
}

interface Props {
	title?: string
	description?: string
	imageAlign: string
	image: {
		data: Media
	}
	faqs: Faq[]
}

defineProps<Props>()
</script>