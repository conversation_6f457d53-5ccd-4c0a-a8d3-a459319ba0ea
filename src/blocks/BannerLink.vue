<template>
	<div class="c-banner container pb-20 md:pb-24">
		<a :href="url" :target="target">
			<img 
				class="mx-auto max-md:hidden md:block" 
				:src="useFormatImage(bannerImage.data.attributes.url)" 
				:alt="bannerImage.data.attributes.alternativeText" 
			/>
			<img 
				class="block mx-auto md:hidden" 
				:src="useFormatImage(mobileBannerImage.data.attributes.url)" 
				:alt="mobileBannerImage.data.attributes.alternativeText" 
			/>
		</a>
	</div>
  </template>
<script lang="ts" setup>
import {useFormatImage} from '@/composables/helpers'
interface Props {
	bannerImage: {
		data: Media
	}
	mobileBannerImage: {
		data: Media
	}
	url: string
	target: '_blank' | '_self'
}

defineProps<Props>()
</script>