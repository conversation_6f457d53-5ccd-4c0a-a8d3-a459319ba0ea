<template>
	<div>
		<div class="container hidden md:flex justify-between items-center mt-5">
			<router-link to="/">
				<DsLogo :style="$store.state.darkNavigation ? 'light' : 'dark'" />
			</router-link>

			<div class="flex gap-4 items-center">
				<div>
					<router-link to="/notices">
						<DsButton href="/notices" label="Network Status" type="outline" />
					</router-link>
				</div>
				<div>
					<DsButton href="https://my.cybersmart.co.za/" label="Client Portal" icon-before="account_circle" :type="$store.state.darkNavigation ? 'outline' : 'secondary'" />
				</div>
			</div>
		</div>

		<div class="container md:hidden my-5">
			<MobileNavigation v-if="navigation" :navigation="navigation" />
		</div>

		<div class="container mt-5 hidden md:flex mb-8">
			<DesktopNavigation v-if="navigation" :navigation="navigation" />
		</div>
		
		<router-view v-slot="{ Component }">
			<transition name="fade">
				<component :is="Component" />
			</transition>
		</router-view>

		<Footer v-if="footerNavigation" :navigation="footerNavigation" />

		<a href="https://help.cybersmart.co.za/" class="fixed bottom-5 right-5 material-symbols-outlined w-16 h-16 flex flex-col items-center justify-center text-white bg-navy rounded-full !text-[42px] shadow-lg border border-white hover:bg-blue transition-all pt-1" target="_blank">contact_support</a>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';

import DesktopNavigation from "@/components/DesktopNavigation.vue";
import MobileNavigation from "@/components/MobileNavigation.vue";
import Footer from "@/components/Footer.vue";
// import PrivacyCookieModal from "@/components/PrivacyCookieModal.vue";


const navigation = ref<any>(null)
const footerNavigation = ref<any>(null)
const networkStatus = ref<any>(null)
const fetchNavigation = async () => {
	try {
		const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}menus/1?nested&populate=*`)
		const data = await response.json()

		if (data.data.id) {
			navigation.value = data.data.attributes.items.data
		}
		else {
			alert('Could not find main navigation');
		}
	} catch (error: any) {
		console.error('An error occurred:', error);
	}

	try {
		const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}menus/2?nested&populate=*`)
		const data = await response.json()

		if (data.data.id) {
			footerNavigation.value = data.data.attributes.items.data
		}
		else {
			alert('Could not find main navigation');
		}
	} catch (error: any) {
		console.error('An error occurred:', error);
	}
}

const fetchNetworkStatus = async () => {
		try {
			const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}network-status?populate=*`);
			const data = await response.json();
			if (data.data.id > 0) {
				networkStatus.value = data.data;
			}
		} catch (error) {
			console.error('An error occurred:', error);
		} finally {
		}
	};

onMounted(() => {
	fetchNavigation()
	fetchNetworkStatus()
})

</script>


<style scoped >
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
}
</style>
