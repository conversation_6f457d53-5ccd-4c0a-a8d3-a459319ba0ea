<template>
	<div>
		<Loader :loading="loading" :message="loaderMessage" :retry="loadeRetry" />
		<Map :address="builtAddress" v-if="showGeoModal && builtAddress" />
		<div class="container">
			<div class="flex max-md:flex-col justify-between items-center gap-12 md:gap-16 mb-20 md:mb-24 mt-12">
				<div class="order-1 max-w-[800px]">
					<DsBreadcrumbs
						v-if="forbusiness"
						:breadcrumbs="[{label: 'For Business', url: '/business-solutions'}, {label: 'Connectivity Options', url: '#'}]"
						class="mb-8 md:mb-20 block"
					/>
					<DsBreadcrumbs
						v-else
						:breadcrumbs="[{label: 'For Home', url: '/home-connectivity'}, {label: 'Connectivity Options', url: '#'}]"
						class="mb-8 md:mb-20 block"
					/>
					<h1 class="text-display text-stone-800 font-semibold">Searched Address:</h1>
					<p class="text-slate-500 font-light mt-8 md:mt-11">{{ buildAddressWithName }}</p>
					<router-link to="/feasibility-search">
						<DsButton
							class="mt-6 border-solid"
							label="Change Address"
							type="outline"
						/>
					</router-link>
				</div>
				<div class="order-0 md:order-2">
					<img class="w-auto h-full object-cover rounded-3xl" :src="thumbImg || `https://maps.googleapis.com/maps/api/staticmap?&markers=${coordsFromMap.lat + ',' + coordsFromMap.lng}${mapPathString}&zoom=${mapsZoom['big']}&size=650x450&maptype=roadmap&key=${mapsKey}`" alt="Google Map location of the address">
				</div>
			</div>
		
			<div class="mb-10">
				<div class="text-h3 text-digital-black font-semibold mb-8">Select Installation:</div>
				<div v-if="productType != 'data-only'" class="flex gap-12 items-center">
					<DsRadioGroup layout="inline" v-model="packageType" :options="packageOptions" @change="setInstallType" name="packageType" />
				</div>
			</div>
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
				<div class="lg:col-span-2 flex flex-col gap-9 lg:gap-12">
					<div class="text-h3 text-digital-black font-semibold">Select Provider:</div>
					<template v-if="zones">
						<div class="flex gap-10 flex-wrap">
							<template v-for="(zone, k) in filteredZones" :key="k">
								<div 
									v-if="Object.keys(zone.promoCodes).length !== 0"
									@click="activeZoneTab = k; $store.commit('setProductSection', `${zone.provider_name}=null`)" 
									class="text-h4 text-digital-black font-semibold cursor-pointer pb-3"
									:class="{ 'border-b-4 border-blue': activeZoneTab == k }"
								>
									{{ !zone.preOrder ? zone.provider_name : zone.provider_name + ' Pre-Order' }}
								</div>
							</template>
						</div>
						<div class="text-h3 text-digital-black font-semibold">Select Package:</div>
						<template v-for="(zone, k) in filteredZones" :key="k">
							<DsTabs v-if="activeZoneTab == k" type="filled">
								<DsTab 
									v-for="(promoCode, index) in zone.promoCodes" 
									:key="index" :label="promoCode.description" 
									:name="`tab-${index}`"
									:active="index === 0"
								>
									<div class="grid grid-cols-1 md:grid-cols-2 gap-12 mt-12">
										<DsPackageCard
											v-for="(promoPackage, index) in promoCode.products"
											:key="index"
											:title="promoPackage.main_product[0].name"
											cta-label="Get this"
											cta-url="#"
											:cost="`R${promoPackage.main_product[0].monthly} per month`"
											:active="selectedProductFull && selectedProduct === promoPackage.promocodeproductid"
											@buttonClicked="selectProduct(promoPackage)"
										>
											<template #moreInfo>
												<p class="font-light">{{promoPackage.main_product[0].details}}</p>
											</template>
										</DsPackageCard>
									</div>
								</DsTab>
							</DsTabs>
						</template>
					</template>
				</div>
				<div ref="selectedProduct">
					<div class="bg-gray-100 rounded-3xl md:sticky md:top-10">
						<div class="py-9 px-8 border-b border-slate-300">
							<div class="text-subtitle text-blue font-semibold flex items-center gap-2">
								<div v-if="showAddressTypeConfirmation" class="material-symbols-outlined">check_circle</div>
								STEP 1: SELECT PACKAGE
							</div>
							<div v-if="selectedProductFull && !showAddressTypeConfirmation" class="">
								<div class="text-h1 text-digital-black font-semibold mb-8">R{{ selectedProductFull.monthly }} pm</div>
								<DsFeatureList :feature-items="formatProductFeatures(selectedProductFull.details)" />
								<DsButton
									label="Continue"
									type="primary"
									class="mt-7"
									@click="confirmAddressType"
								/>
							</div>
						</div>
						<div class="py-9 px-8 border-b border-slate-300">
							<div class="text-subtitle text-blue font-semibold flex items-center gap-2">STEP 2: DETAILS</div>
							<div v-if="showAddressTypeConfirmation" class="">
								<p class="text-slate-500 font-light mb-9">Once we have your details, we can assign a technician to get in touch and get you up and running with Cybersmart connectivity.</p>
								<DsRadioGroup layout="stacked" v-model="typeOfAddress" :options="addressTypeOptions" name="type-of-address" />
								<div class="flex flex-col gap-7 mt-9">
									<div class="flex flex-col gap-7" v-show="typeOfAddress == 'unit'">
										<div class="relative">
											<DsTextInput
												label="Enter your unit / flat number"
												name="unitNumber"
												v-model="unitNumber"
												:invalid="addressSubmitError"
												:valid="!addressSubmitError"
												invalid-message="Please enter a valid unit number."
											/>
										</div>
										<DsTextInput
											label="Enter building name (optional)"
											name="buildingName"
											v-model="buildingName"
										/>
		
									</div>
									<DsTextInput
										label="Your street address"
										name="buildingName"
										v-model="buildAddressWithName"
										:disabled="true"
									/>
									<DsButton
										:label="installType === 'upgrade' ? 'Upgrade/Downgrade' : 'Proceed to Signup'"
										type="primary"
										:disabled="!typeOfAddress"
										@click="submitAddress"
									/>
									<DsButton
										label="Update Address"
										type="outline"
										href="/feasibility-search"
									/>
								</div>
							</div>
						</div>
						<div class="py-9 px-8 text-subtitle text-blue font-semibold">STEP 3: CONFIRM</div>
					</div>
				</div>
			</div>
			<DsModal :open="!showGeoModal && showModalSpecifyAddress" :block-close="true">
				<div class="flex flex-col gap-7">
					<div class="text-stone-800 text-h1 font-semibold">Please specify your address</div>
					<DsTextInput
						v-show="streetNumberError"
						label="Enter your street number"
						v-model="streetNumber"
						name="streetNumber"
					/>
					<DsTextInput
						v-show="streetNameError"
						label="Enter your street name"
						v-model="streetName"
						name="streetName"
					/>
					<DsTextInput
						v-show="suburbError"
						label="Enter your suburb"
						v-model="suburb"
						name="suburb"
					/>
					<DsTextInput
						v-show="postalCodeError"
						label="Enter your postal code"
						v-model="postalCode"
						name="postalCode"
					/>
					<div class="flex gap-4">
						<DsButton
							label="Confirm"
							type="primary"
							@click="specifyAddress"
						/>
						<DsButton
							label="Return to search"
							type="outline"
							:disabled="(!streetNumber && streetNumberError) || (!streetName && streetNameError) || (!suburb && suburbError) || (!postalCode && postalCodeError)"
							@click="returnToSearch"
						/>
					</div>
				</div>
			</DsModal>
		</div>
	</div>
</template>

<script>
import kmlParser from "../parseKml";
import _ from "lodash";
import Loader from "@/components/Loader.vue";
import Map from "@/components/Map.vue";
//import store from "@/store";
// import brand from "@/brand";

export default {
	name: "ProductSelection",
	components: {
		Loader,
		Map
	},
	props: {
		address: String,
		forbusiness: String
	},
	data() {
		return {
			loading: false,
			loaderMessage: null,
			loadeRetry: false,
			productType: null,
			selectedProductFull: null,
			name: null,
			zones: [],
			newAddress: null,
			errors: [],
			showDetails: false,
			showModal: false,
			showModalSpecifyAddress: false,
			selectedAddress: null,
			fullAddress: null,
			addAddress: false,
			typeOfAddress: null,
			streetNumber: null,
			streetName: null,
			suburb: null,
			postalCode: null,
			unitNumber: null,
			buildingName: null,
			streetNumberError: false,
			streetNameError: false,
			suburbError: false,
			postalCodeError: null,
			unitNumberError: null,
			zoneCoordsString: null,
			addressSubmitError: false,
			position: {
				lat: -33.9099856,
				lng: 18.41292670000007,
			},
			limitData: null,
			unitRegex: /^[a-zA-Z]?\d+[a-zA-Z]{0,2}$/,
			mapsZoom: {
				small: 16,
				big: 18
			},
			showGeoModal: false,
			packageType: 'new',
			packageOptions: [
				{ label: 'New Installation', value: 'new' },
				{ label: 'Move from another ISP/ reactivate a dormant installation', value: 'existing' },
				{ label: 'Upgrade / Downgrade', value: 'upgrade' },
			],
			addressTypeOptions: [
				{ label: 'Freestanding House/Dwelling', value: 'streetAddress' },
				{ label: 'Building/Complex', value: 'unit' },
			],
			showAddressTypeConfirmation: false,
			activeZoneTab: 0,
			productsLoaded: false,
		};
	},
	mounted() {
		this.loadProducts();
	},
	computed: {
		// showLightspeedLogo() {
		// 	const showLogo = {
		// 		"gigalight": false,
		// 		"ftta": false,
		// 		"cybersmart": true
		// 	};

		// 	return showLogo[brand.getBrand()];
		// },
		mapPathString() {
			return this.zoneCoordsString ? `&path=color:blue|fillcolor:blue|weight:2|${this.zoneCoordsString}` : "";
		},
		promoCodeId() {
			return this.$store.getters.getPromoCodeId;
		},
		installType() {
			return this.$store.getters.getInstallType;
		},
		// provinces() {
		// 	return this.$store.getters.getProvinces;
		// },
		coordsFromMap() {
			return this.$store.getters.getCoordsFromMap;
		},
		productSection() {
			return (
				this.$store.getters.getProductSection ||
				(this.zones.length ? `${this.zones[0].provider_name}=null` : null)
			);
		},
		selectedProduct() {
			return this.$store.getters.getSelectedProduct;
		},
		codeKey() {
			return this.productSection ? this.productSection.split("=") : null;
		},
		zone() {
			return this.codeKey
				? _.find(this.zones, { provider_name: this.codeKey[0] })
				: null;
		},
		activeProducts() {
			const tempList = {};
			console.log(this.codeKey)
			if (this.zone) {
				if (this.codeKey[1] == "null") {
					_.each(this.zone.promoCodes, (prod) => {
						_.each(prod.products, (val) => {
							tempList["#" + val.promocodeproductid] = val.main_product[0];
							tempList["#" + val.promocodeproductid].promocodeproductid = val.promocodeproductid;
						});
					});
				} else {
					_.each(this.zone.promoCodes[this.codeKey[1]].products, (val) => {
						tempList["#" + val.promocodeproductid] = val.main_product[0];
						tempList["#" + val.promocodeproductid].promocodeproductid = val.promocodeproductid;
					});
				}
			}
			return tempList;
		},
		thumbImg() {
			return this.zone ? this.zone.image || null : null;
		},
		builtAddress() {
			return this.$store.getters.getBuiltAddress;
		},
		buildAddressWithName() {
			return this.name ? this.name + ", " + this.$store.getters.getBuiltAddress : this.$store.getters.getBuiltAddress;
		},
		addresses() {
			return this.zone
				? _.isArray(this.zone.addresses[0])
					? this.zone.addresses[0]
					: this.zone.addresses
				: null;
		},
		addressCode() {
			return this.zone ? this.zone.code : null;
		},
		productIds() {
			return {
				promocodeproductid: this.activeProducts["#" + this.selectedProduct]
					? this.activeProducts["#" + this.selectedProduct].promocodeproductid
					: null,
				productid: this.activeProducts["#" + this.selectedProduct]
					? this.activeProducts["#" + this.selectedProduct].productid
					: null,
				promocodeid: this.getSelectedPromoCodeId(this.selectedProduct),
			};
		},
		mapsKey() {
			return import.meta.env.VITE_MAPS_API_KEY;
		},
		productDescription() {
			return this.activeProducts["#" + this.selectedProduct]
				? this.activeProducts["#" + this.selectedProduct].description
				: null;
		},
		productDetails() {
			if (this.activeProducts["#" + this.selectedProduct]) {
				const { details } = this.activeProducts["#" + this.selectedProduct];
				return details.split(/\r?\n/g);
			}
			return null;
		},
		filteredZones() {
			if (this.forbusiness === 'true') {
				const filteredZones = this.zones.map(zone => {
					if (zone.promoCodes) {
						const filteredPromoCodes = Object.fromEntries(
							Object.entries(zone.promoCodes).filter(([key]) =>
								["biz", "fttb", "ftte"].includes(key)
							)
						);
						return { ...zone, promoCodes: filteredPromoCodes };
					}
					return zone;
				});

				// Check if all promoCodes objects are empty
				const allPromoCodesEmpty = filteredZones.every(zone => 
					Object.keys(zone.promoCodes || {}).length === 0
				);

				if (allPromoCodesEmpty && this.productsLoaded) {
					this.$router.push({
						name: "business-contact",
						params: {
							noProducts: 'true',
						},
					});
				}

				return filteredZones;
			}
			return this.zones;
		}
	},
	watch: {
		addAddress(val) {
			if (val) this.selectedAddress = null;
		},
		selectedProduct: {
			immediate: true,
			handler(val) {
				this.setPromoCodeId(val);
			},
		},
		selectedProductFull(val) {
			if (val.description.indexOf("data only") >= 0) {
				this.setInstallType("data-only");
				this.productType = "data-only";
			} else if (val.description.indexOf("dsl") >= 0) {
				this.setInstallType("new");
				this.productType = "dsl";
			} else {
				this.productType = "fibre";
			}
		},
	},
	methods: {
		mapLoadError() {
			console.log('Too many points for gmap, skipping... ');
			this.zoneCoordsString = "";
		},
		selectProduct(product) {
			this.selectedProductFull = product.main_product[0]
			this.$store.commit('setSelectedProduct', product.promocodeproductid)

			if (window.innerWidth < 1024) {
				const element = this.$refs.selectedProduct;
				if (element) {
				element.scrollIntoView({ behavior: 'smooth' });
				}
			}
		},
		navigate() {
			this.$router.push({
				name: "purchase",
				params: {
					product: this.selectedProduct,
				},
			});
		},
		setBuiltAddress() {
			let tempAddress;
			const baseAddress = this.newAddress || this.fullAddress;

			if (this.productSection && this.productSection.address) {
				tempAddress = this.productSection.address;
			} else if (baseAddress && baseAddress.route) {
				tempAddress = `${baseAddress.street_number || ""} ${baseAddress.route || baseAddress.street_name
					}, ${baseAddress.political}, ${baseAddress.locality}, ${baseAddress.administrative_area_level_1}, ${baseAddress.postal_code}`.trim();
			} else {
				tempAddress = this.address;
			}
			// debugger
			this.$store.commit(
				"setBuiltAddress",
				`${this.unitNumber ? `Unit ${this.unitNumber}, ` : ""}${tempAddress}`
			);
		},
		setInstallType(installType) {
			this.$store.commit("setInstallType", installType);
		},
		setAddressList(addresses) {
			this.$store.commit("setAddressList", addresses);
		},
		getSelectedPromoCodeId(selectedProduct) {
			let id = null;

			if (this.zone) {
				_.each(this.zone.promoCodes, (pC) => {
					_.each(pC.products, (product) => {
						if (product.promocodeproductid == selectedProduct) {
							id = pC.code;
							return false;
						}
					});
					if (id) {
						return false;
					}
				});
			}
			return id;
		},
		setPromoCodeId(selectedProduct) {
			this.$store.commit("setPromoCodeId", this.getSelectedPromoCodeId(selectedProduct));
		},
		checkAddress(addressComponents) {
			this.streetNumberError = !addressComponents["street_number"];
			this.streetNameError = !addressComponents["route"];
			this.suburbError = !addressComponents["political"];
			this.postalCodeError = !addressComponents["postal_code"];

			this.showModalSpecifyAddress = this.streetNumberError || this.streetAddressError || this.suburbError || this.postalCodeError;

			// console.log("street_number: ", addressComponents["street_number"], ", street_name: ", addressComponents["route"],
			// 	", suburb: ", addressComponents["political"], ", postal code: ", addressComponents["postal_code"]);
		},
		async loadProducts() {
			this.loading = true;
			if (this.$store.getters.getSpecifiedFullAddress) {
				this.fullAddress = this.$store.getters.getSpecifiedFullAddress;
			}

			if (this.address.trim().indexOf(" ") === -1) {	
				const hm = new FormData();
				hm.set("zone", this.address);

				let response = null;
				try {
					response = await this.$http.post(
						`${import.meta.env.VITE_BACKEND_URL}feasibility/lookup_v2`,
						hm
					);
				} catch (e) {
					this.loading = false;
					this.$router.push({ name: "home", params: { error: "Server error, please try again later" } });
					return;
				}

				if (response.data.kml) {
					this.zoneCoordsString = kmlParser.extractCoordFromKml(response.data.kml);
				}

				// build address from response
				this.name = response.data.name;
				const adBreak = response.data.address.split(',');
				this.fullAddress = {
					street_number: null,
					route: adBreak[0].trim(),
					political: adBreak[1].trim(),
					locality: response.data.city,
					administrative_area_level_2: response.data.city,
					administrative_area_level_1: response.data.province,
					country: "ZA",
					postal_code: response.data.postal_code,
				};

				this.zones.push(response.data);
				this.setBuiltAddress();
				this.loading = false;
				this.productsLoaded = true;
			} else {
				// Wait for Google Maps to be loaded
				await this.$gmapApiPromiseLazy();
    
				// Create a new Geocoder instance
				const geocoder = new google.maps.Geocoder();

				try {
					const results = await geocoder.geocode({ address: this.address })

					if (results.results.length > 0) {
						this.position.lat = results.results[0].geometry.location.lat()
						this.position.lng = results.results[0].geometry.location.lng()
						// set limit data for new address search
						this.limitData = {
							radius: import.meta.env.VITE_MAPS_LIMIT_RANGE || 5000,
							location: results.results[0].geometry.location,
						}

						if (!this.$store.getters.getSpecifiedFullAddress) {
							const addressArray = {}

							results.results[0].address_components.forEach((component) => {
								addressArray[component.types[0]] = component.long_name
								if (component.types[0] === "route" || component.types[0] === "street_name") {
									addressArray["routeShort"] = component.short_name
								}
							})

							this.fullAddress = addressArray
							this.$store.commit("setSpecifiedFullAddress", this.fullAddress)
						}
						this.showGeoModal = true
						this.checkAddress(this.fullAddress)

						await this.getProducts()
					}
				} catch (error) {
					this.$router.push({ name: "home", params: { error: "Geocoding failed, please try again later" } })
				} finally {
					this.loading = false
					this.productsLoaded = true;
				}
			}
		},
		async specifyAddress() {
			if (this.streetNumber) {
				console.log("New street number: ", this.streetNumber);
				this.fullAddress["street_number"] = this.streetNumber;
			}
			if (this.streetName) {
				console.log("New street name: ", this.streetName);
				this.fullAddress["route"] = this.streetName;
			}
			if (this.suburb) {
				console.log("New suburb: ", this.suburb);
				this.fullAddress["political"] = this.suburb;
			}
			if (this.postalCode) {
				console.log("New postal code: ", this.postalCode);
				this.fullAddress["postal_code"] = this.postalCode;
			}

			this.$store.commit("setSpecifiedFullAddress", this.fullAddress);

			this.setBuiltAddress();

			this.showModalSpecifyAddress = false;
		},
		returnToSearch() {
			this.$router.push({
				name: "home"
			});
		},
		submitAddress() {
			// check for existing address / zone
			const address = this.newAddress || this.fullAddress;

			// If unit address type is chosen check unit number format is valid
			if (this.typeOfAddress === "unit") {
				this.addressSubmitError = false;
				if (!this.unitNumber || !this.unitRegex.test(this.unitNumber.trim())) {
					this.addressSubmitError = true;
					return;
				} else {
					this.unitNumberError = "";
				}
			} else {
				this.unitNumber = null;
			}

			this.setBuiltAddress();
			this.setAddressList(this.addresses);
			this.setPromoCodeId(this.selectedProduct);
			this.$store.commit("setZone", this.zone);
			let tempPosition = {};
			if (Object.keys(this.coordsFromMap).length) {
				tempPosition = this.coordsFromMap
			} else {
				tempPosition = this.position;
			}

			this.$store.commit("setPosition", tempPosition);
			this.$store.commit('setProduct', this.activeProducts["#" + this.selectedProduct] || '{}');
			this.$store.commit('setProductIds', this.productIds || '[]');
			this.$store.commit('setFullAddress', address || '');
			this.$store.commit('setUnitNumber', this.unitNumber || '');
			this.$store.commit('setBuildingName', this.buildingName || '');
			this.$store.commit('setAddressCode', this.addressCode || '');
			this.$store.commit('setInstallType', this.installType || '');
			
			this.$router.push({
				name: "purchase",
			});
		},
		adjustHeight() {
			const elFrom = document.querySelector(".modal-card-body");
			// set scroll position in px
			elFrom.scrollLeft = 0;
			elFrom.scrollTop = elFrom.offsetHeight;
		},
		async getProducts() {
			this.loading = true;
			let hm = new FormData();
			hm.set("latitude", this.coordsFromMap.lat || this.position.lat);
			hm.set("longitude", this.coordsFromMap.lng || this.position.lng);
			hm.set("address", this.address);

			hm.set("fullAddress", JSON.stringify(this.fullAddress));

			let response = null;
			try {
				response = await this.$http.post(
					`${import.meta.env.VITE_BACKEND_URL}feasibility/find_v2`,
					hm
				);
				this.zones = response.data;
				this.setBuiltAddress();
				

				this.zoneCoordsString = "";
				let kml = "";
				if (this.zones[0].preOrder) {
					kml = response.data[0].planned_kml
				} else {
					kml = response.data[0].kml;
				}

				if (kml) {
					this.zoneCoordsString = kmlParser.extractCoordFromKml(kml);
					this.mapsZoom['big'] = "15";
					this.mapsZoom['small'] = "15";
				}

				// debugger
			} catch (e) {
				this.$router.push({ name: "home", params: { error: "Server error, please try again later" } });
				//TODO: print error
			} finally {
				this.loading = false;
			}
		},
		formatProductFeatures(details) {
			const lines = details.split('\r\n');
			
			// Trim any leading or trailing whitespace from each line (optional)
			return lines.map(line => line.trim());
		},
		
		confirmAddressType() {
			this.showAddressTypeConfirmation = true
		}
	},

};
</script>