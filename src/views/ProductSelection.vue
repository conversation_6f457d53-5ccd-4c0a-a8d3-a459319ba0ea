<template>
	<div>
		<Loader :loading="loading" :message="loaderMessage" :retry="loadeRetry" />
		<Map :address="builtAddress" v-if="showGeoModal && builtAddress" />
		<div class="container">
			<div class="flex max-md:flex-col justify-between items-center gap-12 md:gap-16 mb-20 md:mb-24 mt-12">
				<div class="order-1 max-w-[800px]">
					<DsBreadcrumbs
						v-if="forbusiness"
						:breadcrumbs="[{label: 'For Business', url: '/business-solutions'}, {label: 'Connectivity Options', url: '#'}]"
						class="mb-8 md:mb-20 block"
					/>
					<DsBreadcrumbs
						v-else
						:breadcrumbs="[{label: 'For Home', url: '/home-connectivity'}, {label: 'Connectivity Options', url: '#'}]"
						class="mb-8 md:mb-20 block"
					/>
					<h1 class="text-display text-stone-800 font-semibold">Searched Address:</h1>
					<p class="text-slate-500 font-light mt-8 md:mt-11">{{ buildAddressWithName }}</p>
					<router-link to="/feasibility-search">
						<DsButton
							class="mt-6 border-solid"
							label="Change Address"
							type="outline"
						/>
					</router-link>
				</div>
				<div class="order-0 md:order-2">
					<img class="w-auto h-full object-cover rounded-3xl" :src="thumbImg || `https://maps.googleapis.com/maps/api/staticmap?&markers=${coordsFromMap.lat + ',' + coordsFromMap.lng}${mapPathString}&zoom=${mapsZoom['big']}&size=650x450&maptype=roadmap&key=${mapsKey}`" alt="Google Map location of the address">
				</div>
			</div>
		
			<div class="mb-10">
				<div class="text-h3 text-digital-black font-semibold mb-8">Select Installation:</div>
				<div v-if="productType != 'data-only'" class="flex gap-12 items-center">
					<DsRadioGroup layout="inline" v-model="packageType" :options="packageOptions" @change="setInstallType" name="packageType" />
				</div>
			</div>
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
				<div class="lg:col-span-2 flex flex-col gap-9 lg:gap-12">
					<div class="text-h3 text-digital-black font-semibold">Select Provider:</div>
					<template v-if="zones">
						<div class="flex gap-10 flex-wrap">
							<template v-for="(zone, k) in filteredZones" :key="k">
								<div 
									v-if="Object.keys(zone.promoCodes).length !== 0"
									@click="activeZoneTab = k; store.commit('setProductSection', `${zone.provider_name}=null`)"
									class="text-h4 text-digital-black font-semibold cursor-pointer pb-3"
									:class="{ 'border-b-4 border-blue': activeZoneTab == k }"
								>
									{{ !zone.preOrder ? zone.provider_name : zone.provider_name + ' Pre-Order' }}
								</div>
							</template>
						</div>
						<div class="text-h3 text-digital-black font-semibold">Select Package:</div>
						<template v-for="(zone, k) in filteredZones" :key="k">
							<DsTabs v-if="activeZoneTab == k" type="filled">
								<DsTab 
									v-for="(promoCode, index) in zone.promoCodes" 
									:key="index" :label="promoCode.description" 
									:name="`tab-${index}`"
									:active="index === 0"
								>
									<div class="grid grid-cols-1 md:grid-cols-2 gap-12 mt-12">
										<DsPackageCard
											v-for="(promoPackage, index) in promoCode.products"
											:key="index"
											:title="promoPackage.main_product[0].name"
											cta-label="Get this"
											cta-url="#"
											:cost="`R${promoPackage.main_product[0].monthly} per month`"
											:active="selectedProductFull && selectedProductId === promoPackage.promocodeproductid"
											@buttonClicked="selectProduct(promoPackage)"
										>
											<template #moreInfo>
												<p class="font-light">{{promoPackage.main_product[0].details}}</p>
											</template>
										</DsPackageCard>
									</div>
								</DsTab>
							</DsTabs>
						</template>
					</template>
				</div>
				<div ref="selectedProduct">
					<div class="bg-gray-100 rounded-3xl md:sticky md:top-10">
						<div class="py-9 px-8 border-b border-slate-300">
							<div class="text-subtitle text-blue font-semibold flex items-center gap-2">
								<div v-if="showAddressTypeConfirmation" class="material-symbols-outlined">check_circle</div>
								STEP 1: SELECT PACKAGE
							</div>
							<div v-if="selectedProductFull && !showAddressTypeConfirmation" class="">
								<div class="text-h1 text-digital-black font-semibold mb-8">R{{ selectedProductFull.monthly }} pm</div>
								<DsFeatureList :feature-items="formatProductFeatures(selectedProductFull.details)" />
								<DsButton
									label="Continue"
									type="primary"
									class="mt-7"
									@click="confirmAddressType"
								/>
							</div>
						</div>
						<div class="py-9 px-8 border-b border-slate-300">
							<div class="text-subtitle text-blue font-semibold flex items-center gap-2">STEP 2: DETAILS</div>
							<div v-if="showAddressTypeConfirmation" class="">
								<p class="text-slate-500 font-light mb-9">Once we have your details, we can assign a technician to get in touch and get you up and running with Cybersmart connectivity.</p>
								<DsRadioGroup layout="stacked" v-model="typeOfAddress" :options="addressTypeOptions" name="type-of-address" />
								<div class="flex flex-col gap-7 mt-9">
									<div class="flex flex-col gap-7" v-show="typeOfAddress == 'unit'">
										<div class="relative">
											<DsTextInput
												label="Enter your unit / flat number"
												name="unitNumber"
												v-model="unitNumber"
												:invalid="addressSubmitError"
												:valid="!addressSubmitError"
												invalid-message="Please enter a valid unit number."
											/>
										</div>
										<DsTextInput
											label="Enter building name (optional)"
											name="buildingName"
											v-model="buildingName"
										/>
		
									</div>
									<DsTextInput
										label="Your street address"
										name="buildingName"
										v-model="buildAddressWithName"
										:disabled="true"
									/>
									<DsButton
										:label="installType === 'upgrade' ? 'Upgrade/Downgrade' : 'Proceed to Signup'"
										type="primary"
										:disabled="!typeOfAddress"
										@click="submitAddress"
									/>
									<DsButton
										label="Update Address"
										type="outline"
										href="/feasibility-search"
									/>
								</div>
							</div>
						</div>
						<div class="py-9 px-8 text-subtitle text-blue font-semibold">STEP 3: CONFIRM</div>
					</div>
				</div>
			</div>
			<DsModal :open="!showGeoModal && showModalSpecifyAddress" :block-close="true">
				<div class="flex flex-col gap-7">
					<div class="text-stone-800 text-h1 font-semibold">Please specify your address</div>
					<DsTextInput
						v-show="streetNumberError"
						label="Enter your street number"
						v-model="streetNumber"
						name="streetNumber"
					/>
					<DsTextInput
						v-show="streetNameError"
						label="Enter your street name"
						v-model="streetName"
						name="streetName"
					/>
					<DsTextInput
						v-show="suburbError"
						label="Enter your suburb"
						v-model="suburb"
						name="suburb"
					/>
					<DsTextInput
						v-show="postalCodeError"
						label="Enter your postal code"
						v-model="postalCode"
						name="postalCode"
					/>
					<div class="flex gap-4">
						<DsButton
							label="Confirm"
							type="primary"
							@click="specifyAddress"
						/>
						<DsButton
							label="Return to search"
							type="outline"
							:disabled="(!streetNumber && streetNumberError) || (!streetName && streetNameError) || (!suburb && suburbError) || (!postalCode && postalCodeError)"
							@click="returnToSearch"
						/>
					</div>
				</div>
			</DsModal>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import kmlParser from "../parseKml";
import _ from "lodash";
import Loader from "@/components/Loader.vue";
import Map from "@/components/Map.vue";

// Define props
interface Props {
	address: string;
	forbusiness?: string;
}

const props = withDefaults(defineProps<Props>(), {
	forbusiness: undefined
});

// Get Vue instance for accessing global properties
const instance = getCurrentInstance();
const $http = instance?.appContext.config.globalProperties.$http;
const $gmapApiPromiseLazy = instance?.appContext.config.globalProperties.$gmapApiPromiseLazy;

// Router and store
const router = useRouter();
const store = useStore();

// Reactive data
const loading = ref(false);
const loaderMessage = ref<string | null>(null);
const loadeRetry = ref(false);
const productType = ref<string | null>(null);
const selectedProductFull = ref<any>(null);
const name = ref<string | null>(null);
const zones = ref<any[]>([]);
const newAddress = ref<any>(null);
const errors = ref<any[]>([]);
const showDetails = ref(false);
const showModal = ref(false);
const showModalSpecifyAddress = ref(false);
const selectedAddress = ref<any>(null);
const fullAddress = ref<any>(null);
const addAddress = ref(false);
const typeOfAddress = ref<string | null>(null);
const streetNumber = ref<string | null>(null);
const streetName = ref<string | null>(null);
const suburb = ref<string | null>(null);
const postalCode = ref<string | null>(null);
const unitNumber = ref<string | null>(null);
const buildingName = ref<string | null>(null);
const streetNumberError = ref(false);
const streetNameError = ref(false);
const suburbError = ref(false);
const postalCodeError = ref<any>(null);
const unitNumberError = ref<any>(null);
const zoneCoordsString = ref<string | null>(null);
const addressSubmitError = ref(false);
const position = reactive({
	lat: -33.9099856,
	lng: 18.41292670000007,
});
const limitData = ref<any>(null);
const unitRegex = /^[a-zA-Z]?\d+[a-zA-Z]{0,2}$/;
const mapsZoom = reactive({
	small: 16 as number | string,
	big: 18 as number | string
});
const showGeoModal = ref(false);
const packageType = ref('new');
const packageOptions = [
	{ label: 'New Installation', value: 'new' },
	{ label: 'Move from another ISP/ reactivate a dormant installation', value: 'existing' },
	{ label: 'Upgrade / Downgrade', value: 'upgrade' },
];
const addressTypeOptions = [
	{ label: 'Freestanding House/Dwelling', value: 'streetAddress' },
	{ label: 'Building/Complex', value: 'unit' },
];
const showAddressTypeConfirmation = ref(false);
const activeZoneTab = ref(0);
const productsLoaded = ref(false);

// Template ref
const selectedProduct = ref<HTMLElement>();
// Computed properties
const mapPathString = computed(() => {
	return zoneCoordsString.value ? `&path=color:blue|fillcolor:blue|weight:2|${zoneCoordsString.value}` : "";
});

const promoCodeId = computed(() => {
	return store.getters.getPromoCodeId;
});

const installType = computed(() => {
	return store.getters.getInstallType;
});

const coordsFromMap = computed(() => {
	return store.getters.getCoordsFromMap;
});

const productSection = computed(() => {
	return (
		store.getters.getProductSection ||
		(zones.value.length ? `${zones.value[0].provider_name}=null` : null)
	);
});

const selectedProductId = computed(() => {
	return store.getters.getSelectedProduct;
});

const codeKey = computed(() => {
	return productSection.value ? productSection.value.split("=") : null;
});

const zone = computed(() => {
	return codeKey.value
		? _.find(zones.value, { provider_name: codeKey.value[0] })
		: null;
});
const activeProducts = computed(() => {
	const tempList: any = {};
	console.log(codeKey.value)
	if (zone.value) {
		if (codeKey.value && codeKey.value[1] == "null") {
			_.each(zone.value.promoCodes, (prod: any) => {
				_.each(prod.products, (val: any) => {
					tempList["#" + val.promocodeproductid] = val.main_product[0];
					tempList["#" + val.promocodeproductid].promocodeproductid = val.promocodeproductid;
				});
			});
		} else if (codeKey.value) {
			_.each(zone.value.promoCodes[codeKey.value[1]].products, (val: any) => {
				tempList["#" + val.promocodeproductid] = val.main_product[0];
				tempList["#" + val.promocodeproductid].promocodeproductid = val.promocodeproductid;
			});
		}
	}
	return tempList;
});

const thumbImg = computed(() => {
	return zone.value ? zone.value.image || null : null;
});

const builtAddress = computed(() => {
	return store.getters.getBuiltAddress;
});

const buildAddressWithName = computed(() => {
	return name.value ? name.value + ", " + store.getters.getBuiltAddress : store.getters.getBuiltAddress;
});

const addresses = computed(() => {
	return zone.value
		? _.isArray(zone.value.addresses[0])
			? zone.value.addresses[0]
			: zone.value.addresses
		: null;
});

const addressCode = computed(() => {
	return zone.value ? zone.value.code : null;
});
const productIds = computed(() => {
	return {
		promocodeproductid: activeProducts.value["#" + selectedProductId.value]
			? activeProducts.value["#" + selectedProductId.value].promocodeproductid
			: null,
		productid: activeProducts.value["#" + selectedProductId.value]
			? activeProducts.value["#" + selectedProductId.value].productid
			: null,
		promocodeid: getSelectedPromoCodeId(selectedProductId.value),
	};
});

const mapsKey = computed(() => {
	return import.meta.env.VITE_MAPS_API_KEY;
});

const productDescription = computed(() => {
	return activeProducts.value["#" + selectedProductId.value]
		? activeProducts.value["#" + selectedProductId.value].description
		: null;
});

const productDetails = computed(() => {
	if (activeProducts.value["#" + selectedProductId.value]) {
		const { details } = activeProducts.value["#" + selectedProductId.value];
		return details.split(/\r?\n/g);
	}
	return null;
});
const filteredZones = computed(() => {
	if (props.forbusiness === 'true') {
		const filteredZonesData = zones.value.map(zone => {
			if (zone.promoCodes) {
				const filteredPromoCodes = Object.fromEntries(
					Object.entries(zone.promoCodes).filter(([key]) =>
						["biz", "fttb", "ftte"].includes(key)
					)
				);
				return { ...zone, promoCodes: filteredPromoCodes };
			}
			return zone;
		});

		// Check if all promoCodes objects are empty
		const allPromoCodesEmpty = filteredZonesData.every(zone =>
			Object.keys(zone.promoCodes || {}).length === 0
		);

		if (allPromoCodesEmpty && productsLoaded.value) {
			router.push({
				name: "business-contact",
				params: {
					noProducts: 'true',
				},
			});
		}

		return filteredZonesData;
	}
	return zones.value;
});

// Methods
const mapLoadError = () => {
	console.log('Too many points for gmap, skipping... ');
	zoneCoordsString.value = "";
};

const selectProduct = (product: any) => {
	selectedProductFull.value = product.main_product[0];
	store.commit('setSelectedProduct', product.promocodeproductid);

	if (window.innerWidth < 1024) {
		const element = selectedProduct.value;
		if (element) {
			element.scrollIntoView({ behavior: 'smooth' });
		}
	}
};

const navigate = () => {
	router.push({
		name: "purchase",
		params: {
			product: selectedProductId.value,
		},
	});
};
const setBuiltAddress = () => {
	let tempAddress;
	const baseAddress = newAddress.value || fullAddress.value;

	if (productSection.value && productSection.value.address) {
		tempAddress = productSection.value.address;
	} else if (baseAddress && baseAddress.route) {
		tempAddress = `${baseAddress.street_number || ""} ${baseAddress.route || baseAddress.street_name
			}, ${baseAddress.political}, ${baseAddress.locality}, ${baseAddress.administrative_area_level_1}, ${baseAddress.postal_code}`.trim();
	} else {
		tempAddress = props.address;
	}
	// debugger
	store.commit(
		"setBuiltAddress",
		`${unitNumber.value ? `Unit ${unitNumber.value}, ` : ""}${tempAddress}`
	);
};

const setInstallType = (installTypeValue: string) => {
	store.commit("setInstallType", installTypeValue);
};

const setAddressList = (addressesValue: any) => {
	store.commit("setAddressList", addressesValue);
};
const getSelectedPromoCodeId = (selectedProductValue: any): any => {
	let id: any = null;

	if (zone.value) {
		_.each(zone.value.promoCodes, (pC: any) => {
			_.each(pC.products, (product: any) => {
				if (product.promocodeproductid == selectedProductValue) {
					id = pC.code;
					return false;
				}
			});
			if (id) {
				return false;
			}
		});
	}
	return id;
};

const setPromoCodeId = (selectedProductValue: any) => {
	store.commit("setPromoCodeId", getSelectedPromoCodeId(selectedProductValue));
};

const checkAddress = (addressComponents: any) => {
	streetNumberError.value = !addressComponents["street_number"];
	streetNameError.value = !addressComponents["route"];
	suburbError.value = !addressComponents["political"];
	postalCodeError.value = !addressComponents["postal_code"];

	showModalSpecifyAddress.value = streetNumberError.value || streetNameError.value || suburbError.value || postalCodeError.value;

	// console.log("street_number: ", addressComponents["street_number"], ", street_name: ", addressComponents["route"],
	// 	", suburb: ", addressComponents["political"], ", postal code: ", addressComponents["postal_code"]);
};
const loadProducts = async () => {
	loading.value = true;
	if (store.getters.getSpecifiedFullAddress) {
		fullAddress.value = store.getters.getSpecifiedFullAddress;
	}

	if (props.address.trim().indexOf(" ") === -1) {
		const hm = new FormData();
		hm.set("zone", props.address);

		let response = null;
		try {
			response = await $http.post(
				`${import.meta.env.VITE_BACKEND_URL}feasibility/lookup_v2`,
				hm
			);
		} catch (e) {
			loading.value = false;
			router.push({ name: "home", params: { error: "Server error, please try again later" } });
			return;
		}

		if (response.data.kml) {
			zoneCoordsString.value = kmlParser.extractCoordFromKml(response.data.kml);
		}

		// build address from response
		name.value = response.data.name;
		const adBreak = response.data.address.split(',');
		fullAddress.value = {
			street_number: null,
			route: adBreak[0].trim(),
			political: adBreak[1].trim(),
			locality: response.data.city,
			administrative_area_level_2: response.data.city,
			administrative_area_level_1: response.data.province,
			country: "ZA",
			postal_code: response.data.postal_code,
		};

		zones.value.push(response.data);
		setBuiltAddress();
		loading.value = false;
		productsLoaded.value = true;
	} else {
		// Wait for Google Maps to be loaded
		await $gmapApiPromiseLazy();

		// Create a new Geocoder instance
		const geocoder = new (window as any).google.maps.Geocoder();

		try {
			const results = await geocoder.geocode({ address: props.address })

			if (results.results.length > 0) {
				position.lat = results.results[0].geometry.location.lat()
				position.lng = results.results[0].geometry.location.lng()
				// set limit data for new address search
				limitData.value = {
					radius: import.meta.env.VITE_MAPS_LIMIT_RANGE || 5000,
					location: results.results[0].geometry.location,
				}

				if (!store.getters.getSpecifiedFullAddress) {
					const addressArray: any = {}

					results.results[0].address_components.forEach((component: any) => {
						addressArray[component.types[0]] = component.long_name
						if (component.types[0] === "route" || component.types[0] === "street_name") {
							addressArray["routeShort"] = component.short_name
						}
					})

					fullAddress.value = addressArray
					store.commit("setSpecifiedFullAddress", fullAddress.value)
				}
				showGeoModal.value = true
				checkAddress(fullAddress.value)

				await getProducts()
			}
		} catch (error) {
			router.push({ name: "home", params: { error: "Geocoding failed, please try again later" } })
		} finally {
			loading.value = false
			productsLoaded.value = true;
		}
	}
};
const specifyAddress = async () => {
	if (streetNumber.value) {
		console.log("New street number: ", streetNumber.value);
		fullAddress.value["street_number"] = streetNumber.value;
	}
	if (streetName.value) {
		console.log("New street name: ", streetName.value);
		fullAddress.value["route"] = streetName.value;
	}
	if (suburb.value) {
		console.log("New suburb: ", suburb.value);
		fullAddress.value["political"] = suburb.value;
	}
	if (postalCode.value) {
		console.log("New postal code: ", postalCode.value);
		fullAddress.value["postal_code"] = postalCode.value;
	}

	store.commit("setSpecifiedFullAddress", fullAddress.value);

	setBuiltAddress();

	showModalSpecifyAddress.value = false;
};

const returnToSearch = () => {
	router.push({
		name: "home"
	});
};
const submitAddress = () => {
	// check for existing address / zone
	const address = newAddress.value || fullAddress.value;

	// If unit address type is chosen check unit number format is valid
	if (typeOfAddress.value === "unit") {
		addressSubmitError.value = false;
		if (!unitNumber.value || !unitRegex.test(unitNumber.value.trim())) {
			addressSubmitError.value = true;
			return;
		} else {
			unitNumberError.value = "";
		}
	} else {
		unitNumber.value = null;
	}

	setBuiltAddress();
	setAddressList(addresses.value);
	setPromoCodeId(selectedProductId.value);
	store.commit("setZone", zone.value);
	let tempPosition = {};
	if (Object.keys(coordsFromMap.value).length) {
		tempPosition = coordsFromMap.value
	} else {
		tempPosition = position;
	}

	store.commit("setPosition", tempPosition);
	store.commit('setProduct', activeProducts.value["#" + selectedProductId.value] || '{}');
	store.commit('setProductIds', productIds.value || '[]');
	store.commit('setFullAddress', address || '');
	store.commit('setUnitNumber', unitNumber.value || '');
	store.commit('setBuildingName', buildingName.value || '');
	store.commit('setAddressCode', addressCode.value || '');
	store.commit('setInstallType', installType.value || '');

	router.push({
		name: "purchase",
	});
};
const adjustHeight = () => {
	const elFrom = document.querySelector(".modal-card-body") as HTMLElement;
	// set scroll position in px
	if (elFrom) {
		elFrom.scrollLeft = 0;
		elFrom.scrollTop = elFrom.offsetHeight;
	}
};

const getProducts = async () => {
	loading.value = true;
	let hm = new FormData();
	hm.set("latitude", coordsFromMap.value.lat || position.lat);
	hm.set("longitude", coordsFromMap.value.lng || position.lng);
	hm.set("address", props.address);

	hm.set("fullAddress", JSON.stringify(fullAddress.value));

	let response = null;
	try {
		response = await $http.post(
			`${import.meta.env.VITE_BACKEND_URL}feasibility/find_v2`,
			hm
		);
		zones.value = response.data;
		setBuiltAddress();


		zoneCoordsString.value = "";
		let kml = "";
		if (zones.value[0].preOrder) {
			kml = response.data[0].planned_kml
		} else {
			kml = response.data[0].kml;
		}

		if (kml) {
			zoneCoordsString.value = kmlParser.extractCoordFromKml(kml);
			mapsZoom['big'] = "15";
			mapsZoom['small'] = "15";
		}

		// debugger
	} catch (e) {
		router.push({ name: "home", params: { error: "Server error, please try again later" } });
		//TODO: print error
	} finally {
		loading.value = false;
	}
};
const formatProductFeatures = (details: string) => {
	const lines = details.split('\r\n');

	// Trim any leading or trailing whitespace from each line (optional)
	return lines.map(line => line.trim());
};

const confirmAddressType = () => {
	showAddressTypeConfirmation.value = true;
};

// Lifecycle hook
onMounted(() => {
	loadProducts();
});
</script>