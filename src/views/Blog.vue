<template>
	<div>
		<FeasibilitySearch
			subtitle="Find your fibre"
			title="Search available connectivity"
			description="Enter your address below to find the best fibre options available to you."
		/>
		<div class="py-26">
		
			<Hero subtitle="Cybersmart Blog" title="SMART news"
				description="Fibre To The Home (FTTH) or Fibre To The Business (FTTB), WiFi, legal updates and privacy policies, new routers, old connections and updates to the internet at large - If it's Smart news, you'll find it here." />
			<div class="container">
				<div class="max-lg:hidden border-b border-slate-300">
					<div class="flex items-center">
						<div @click="categoryFilter = ''" class="text-base text-digital-black p-4 cursor-pointer"
							:class="{ 'border-b-4 border-blue': !categoryFilter }">All News</div>
						<div @click="categoryFilter = formatCategoryValue(category.name)" v-for="category in categories"
							:key="category.name" class="text-base text-digital-black p-4 cursor-pointer"
							:class="{ 'border-b-4 border-blue': categoryFilter == formatCategoryValue(category.name) }">{{
								category.name }}</div>
					</div>
				</div>
				<div class="lg:hidden mb-11">
					<DsSelectInput label="Filter by Category" v-model="categoryFilter" :options="categoryOptions" />
				</div>
				<div class="grid grid-cols-1 lg:grid-cols-2 gap-x-20 gap-y-10 my-12 lg:my-16">
					<DsPostCard v-for="post in filteredPosts" :key="post.id" :image="post.feature" :category="post.category"
						:title="post.title" :excerpt="post.synopsis" :date="post.revealDate"
						@button-clicked="router.push(`/blog/${post.id}`)" />
				</div>
			</div>
		
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import Hero from '@/blocks/Hero.vue';
import FeasibilitySearch from '@/blocks/FeasibilitySearch.vue';

// Reactive state
const posts = ref<Array<any>>([]);
const categories = ref<Array<any>>([]);
const page = ref<number>(0);
const categoryFilter = ref<string>('');
const router = useRouter();

const filteredPosts = computed(() => {
	return posts.value.filter(v => (
		(!categoryFilter.value || formatCategoryValue(v.category) === formatCategoryValue(categoryFilter.value))
	));
});

const categoryOptions = computed(() => {
	var options = {
		label: "All Posts",
		value: ""
	}
	const formattedCategories = categories.value.map(category => {
		return {
			label: category.name,
			value: formatCategoryValue(category.name)
		}
	});

	return [options, ...formattedCategories];
})

// Methods
const loadMorePosts = async () => {
	const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}blogs?_page=${page.value++}`);

	// Check if the response is successful
	if (!response.ok) {
		throw new Error(`Failed to fetch posts: ${response.statusText}`);
	}

	const data = await response.json();

	posts.value = posts.value.concat(
		data
			.map((value: any) => ({
				id: value.id,
				revealDate: new Date(value['revealDate']).toLocaleDateString('en-US', {
					month: 'short',
					day: 'numeric',
					year: 'numeric'
				}),
				revealDateObj: new Date(value['revealDate']), // Keep the original Date object for sorting
				category: value.category?.name,
				categoryId: value.category?.id,
				feature: `${import.meta.env.VITE_BACKEND_BASE_URL}/${value.feature.url.substring(1)}`,
				synopsis: value.synopsis,
				title: value.title
			}))
			.sort((a, b) => b.revealDateObj.getTime() - a.revealDateObj.getTime()) // Sort by revealDateObj in descending order
	).map(post => {
		delete post.revealDateObj; // Optionally remove the temporary revealDateObj after sorting
		return post;
	});

};

const formatCategoryValue = (value: string) => {
	return value.replace(/\s+/g, '-').toLowerCase();
}

// Lifecycle hook
onMounted(async () => {
	loadMorePosts();

	const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}categories`);

	// Check if the response is successful
	if (!response.ok) {
		throw new Error(`Failed to fetch categories: ${response.statusText}`);
	}

	const data = await response.json();
	categories.value = data;
});
</script>
