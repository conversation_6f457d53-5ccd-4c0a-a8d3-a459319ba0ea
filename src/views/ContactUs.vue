<template>
	<div>
		<FeasibilitySearch
			ref="feasibilitySearch"
			subtitle="Find your fibre"
			title="Search available connectivity"
			description="Enter your address below to find the best fibre options available to you."
		/>
		<div class="container mt-20 md:mt-24">
			<div class="grid grid-cols-1 gap-7 lg:grid-cols-2 lg:gap-14 mb-20 md:mb-24">
				<div class="flex flex-col justify-between gap-10">
					<div>
						<div class="text-subtitle text-blue font-semibold mb-4 uppercase">Contact US</div>
						<div class="text-h1 text-digital-black font-semibold mb-12">How can we help you?</div>
						<p class="text-slate-500 font-light">
							Couldn't find what you're looking for on our website? We're here to help - simply fill out the form
							and one of our dedicated ISP experts will get back to you ASAP.
						</p>
					</div>
					<div class="max-lg:hidden bg-blue p-6 md:p-12 rounded-3xl">
						<div class="text-h2 text-white font-semibold mb-7">Looking for business connectivity?</div>
						<p class="text-white font-light mb-3">FTTB or Business Fibre, Cybersmart has Business Connectivity
							Solutions tailored to your needs.</p>
						<div class="flex items-center gap-2">
							<router-link to="business-contact">
								<DsButton label="Get In Touch" href="/business-contact" type="secondary" />
							</router-link>
							<router-link to="/business-solutions">
								<DsButton label="Learn More" href="/business-solutions" type="outline" />
							</router-link>
						</div>
					</div>
				</div>
				<div class="bg-slate-50 rounded-3xl px-4 md:px-7 py-12 flex flex-col justify-center">
					<form @submit.prevent="validateAndSubmit" class="flex flex-col gap-5">
						<DsSelectInput
							label="Your query"
							placeholder="Please Select"
							:options="queryOptions"
							v-model="v$.query.$model"
							:valid="formData.query !== ''"
							invalid-message="Please select your query"
							@change="formData.faultType = ''; formData.SupportQuery = ''; formData.BillingQuery = ''; formData.hostingFault = ''; formData.fibreProvider = ''"
						/>

						<DsSelectInput
							v-if="formData.query === 'Fault_Reporting'"
							label="Please select a fault type:"
							placeholder="Please Select"
							:options="faultOptions"
							v-model="formData.faultType"
							:invalid="false"
							:valid="formData.faultType !== ''"
							invalid-message="Please select your query"
						/>

						<DsSelectInput
							v-if="formData.query === 'Support_Query'"
							label="Support Query:"
							placeholder="Please Select"
							:options="supportQueryOptions"
							v-model="formData.SupportQuery"
							:invalid="false"
							:valid="formData.SupportQuery !== ''"
							invalid-message="Please select your query"
						/>

						<DsSelectInput
							v-if="formData.query === 'Billing_Query'"
							label="Billing Query:"
							placeholder="Please Select"
							:options="billingQueryOptions"
							v-model="formData.BillingQuery"
							:invalid="false"
							:valid="formData.BillingQuery !== ''"
							invalid-message="Please select your query"
						/>

						<DsSelectInput
							v-if="formData.faultType === 'Fibre_Fault_Reporting' || formData.SupportQuery === 'Fibre_Support_Query'"
							label="Fibre Provider:"
							placeholder="Please Select"
							:options="fibreProviderOptions"
							v-model="formData.fibreProvider"
							:invalid="false"
							:valid="formData.fibreProvider !== ''"
							invalid-message="Please select your query"
						/>

						<DsSelectInput
							v-if="formData.faultType === 'Hosting_Fault_Reporting'"
							label="Hosting Fault Query Type"
							placeholder="Please Select"
							:options="hostingFaultOptions"
							v-model="formData.hostingFault"
							:invalid="false"
							:valid="formData.hostingFault !== ''"
							invalid-message="Please select your query"
						/>

						<DsSelectInput
							v-if="formData.SupportQuery === 'Hosting_Support_Query'"
							label="Hosting Support Query Type"
							placeholder="Please Select"
							:options="hostingSupportOptions"
							v-model="formData.hostingFault"
							:invalid="false"
							:valid="formData.hostingFault !== ''"
							invalid-message="Please select your query"
						/>

						<DsSelectInput
							v-if="formData.BillingQuery === 'Hosting_Billing_Query'"
							label="Hosting Billing Query Type"
							placeholder="Please Select"
							:options="hostingBillingOptions"
							v-model="formData.hostingFault"
							:invalid="false"
							:valid="formData.hostingFault !== ''"
							invalid-message="Please select your query"
						/>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-5">
							<DsTextInput
								label="Name"
								name="name"
								v-model="v$.name.$model"
								:invalid="v$.name.$dirty && v$.name.$invalid"
								:valid="v$.name.$dirty && !v$.name.$invalid"
								invalid-message="Please enter your name"
							/>
							<DsTextInput
								label="Email"
								name="email"
								v-model="v$.email.$model"
								:invalid="v$.email.$dirty && v$.email.$invalid"
								:valid="v$.email.$dirty && !v$.email.$invalid"
								invalid-message="Please enter a valid email"
							/>
						</div>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-5">
							<DsTextInput
								label="Phone"
								name="phone"
								v-model="v$.phone.$model"
								:invalid="v$.phone.$dirty && v$.phone.$invalid"
								:valid="v$.phone.$dirty && !v$.phone.$invalid"
								invalid-message="Please enter your phone number"
							/>
							<DsTextInput
								label="Company (Optional)"
								name="company"
								v-model="formData.company"
								:valid="formData.company !== ''"
							/>
						</div>
						<DsTextArea
							:label="messageLabel"
							name="message"
							v-model="v$.message.$model"
							:invalid="v$.message.$dirty && v$.message.$invalid"
							:valid="v$.message.$dirty && !v$.message.$invalid"
							invalid-message="Please enter your message"
						/>
						<DsTextInput
							v-if="formData.faultType === 'ADSL_Fault_Reporting' || formData.SupportQuery === 'ADSL_Support_Query' || formData.BillingQuery === 'ADSL_Billing_Query'"
							label="ADSL Username"
							name="adslUsername"
							v-model="formData.adslUsername"
							:valid="formData.adslUsername !== ''"
						/>
						<DsTextInput
							v-if="formData.faultType === 'Fibre_Fault_Reporting' || formData.faultType === 'Hosting_Fault_Reporting' || formData.faultType === 'VOIP_Fault_Reporting' || formData.SupportQuery === 'Fibre_Support_Query' || formData.BillingQuery === 'Fibre_Billing_Query' || formData.hostingFault === 'Domain_Billing_Query' || formData.hostingFault === 'Email_Billing_Query' || formData.hostingFault === 'Cloud_Hosting_Billing_Query' || formData.BillingQuery === 'VOIP_Billing_Query'"
							label="Cybersmart Account Number:"
							name="cyberSmartAccountNumber"
							v-model="formData.cyberSmartAccountNumber"
							:valid="formData.cyberSmartAccountNumber !== ''"
						/>
						<DsTextInput
							v-if="formData.faultType === 'Fibre_Fault_Reporting' || formData.SupportQuery === 'Fibre_Support_Query'"
							label="Location of Service:"
							note="Enter your street number and street address of your house or complex"
							name="locationOfService"
							v-model="formData.locationOfService"
							:valid="formData.locationOfService !== ''"
						/>
						<DsTextInput
							v-if="formData.hostingFault === 'Domain_Fault_Query' || formData.hostingFault === 'Email_Fault_Query' || formData.hostingFault === 'Website_Fault_Query' || formData.hostingFault === 'Domain_Support_Query' || formData.hostingFault === 'Email_Support_Query' || formData.hostingFault === 'Website_Support_Query' || formData.hostingFault === 'Domain_Billing_Query' || formData.hostingFault === 'Email_Billing_Query'"
							label="Applicable Domain Name:"
							name="applicableDomainName"
							v-model="formData.applicableDomainName"
							:valid="formData.applicableDomainName !== ''"
						/>
						<DsTextInput
							v-if="formData.hostingFault === 'Cloud_Hosting_Fault_Query' || formData.hostingFault === 'Cloud_Hosting_Support_Query' || formData.faultType === 'VOIP_Fault_Reporting'  || formData.hostingFault === 'Cloud_Hosting_Billing_Query' || formData.BillingQuery === 'VOIP_Billing_Query'"
							label="Applicable IP Address:"
							name="applicableIpAddress"
							v-model="formData.applicableIpAddress"
							:valid="formData.applicableIpAddress !== ''"
						/>
						<DsTextInput
							v-if="formData.query === 'Installation_Status_Query'"
							label="CSL / Order Reference number:"
							name="cslOrderReferenceNumber"
							v-model="formData.cslOrderReferenceNumber"
							:valid="formData.cslOrderReferenceNumber !== ''"
						/>
						<DsTextInput
							v-if="formData.query === 'Installation_Status_Query'"
							label="Address at which Service was ordered:"
							name="address"
							v-model="formData.address"
							:valid="formData.address !== ''"
						/>
						<div>
							<DsButton button-type="submit" :label="isSubmitting ? 'Sending...' : 'Send Message'" type="primary" :disabled="isSubmitting || v$.$invalid" />
						</div>
						<div v-if="error" class="fixed top-8 inset-x-0 w-fit m-auto">
							<DsToast class="" title="Error" variant="danger z-50">
								{{ error }}
							</DsToast>
						</div>
						<div v-if="success" class="fixed top-8 inset-x-0 w-fit m-auto z-50">
							<DsToast class="" title="Thank you." variant="success">
								Your message has been sent. We'll be in touch shortly
							</DsToast>
						</div>
					</form>
				</div>
				<div class="lg:hidden bg-blue p-6 md:p-12 rounded-3xl">
					<div class="text-h2 text-white font-semibold mb-7">Looking for business connectivity?</div>
					<p class="text-white font-light mb-3">FTTB or Business Fibre, Cybersmart has Business Connectivity
						Solutions tailored to your needs.</p>
					<div class="flex items-center gap-2">
						<div>
							<router-link to="business-contact">
								<DsButton label="Get In Touch" href="/business-contact" type="secondary" />
							</router-link>
						</div>
						<div>
							<router-link to="business-contact">
								<DsButton label="Learn More" href="/business-solutions" type="outline" />
							</router-link>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="container">
			<div class="py-20 md:pb-24">
				<div class="grid grid-cols-1 lg:grid-cols-3 gap-10 justify-between">
					<div class="">
						<div class="text-h1 text-digital-black font-semibold mb-4">Get in touch:</div>
						<div class="text-h3 mb-2">Monday To Friday:</div>
						<div class="mb-4">
							<strong>Business Hours:</strong> 8:00 AM to 8:00 PM<br />
							<strong>Call Support:</strong> 8:00 AM to 1:00 PM<br />
							<strong>WhatsApp Support:</strong> 8:00 AM to 8:00 PM
						</div>
						<div class="text-h3 mb-2">Saturday, Sunday:</div>
						<div>
							 <strong>Business Hours:</strong> 8:00 AM to 5:00 PM<br />
							 <strong>Call Support:</strong> Unavailable<br />
							 <strong>WhatsApp Support & E-mail Support:</strong> 8:00 AM to 5:00 PM
						</div>
					</div>
					<div class="md:col-span-2 lg:flex lg:justify-end">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-[800px]">
							<div class="col-span-1 p-8 bg-slate-50 rounded-3xl">
								<div class="text-h3 font-medium text-digital-black mb-6">Support</div>
								<div class="flex items-center gap-2 mb-4">
									<div class="material-symbols-outlined text-blue">mail</div>
									<a href="mailto:<EMAIL>" class="text-slate-500 text-lg font-light hover:text-blue transition-all"><EMAIL></a>
								</div>
								<div class="flex items-center gap-2 mb-4">
									<div class="material-symbols-outlined text-blue">call</div>
									<a href="tel:0212860123" class="text-slate-500 text-lg font-light hover:text-blue transition-all">************</a>
								</div>
								<div class="flex items-center gap-2">
									<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path class="fill-blue" d="M19.0508 4.91005C18.1338 3.98416 17.0418 3.25002 15.8383 2.75042C14.6348 2.25081 13.3439 1.99574 12.0408 2.00005C6.58078 2.00005 2.13078 6.45005 2.13078 11.9101C2.13078 13.6601 2.59078 15.3601 3.45078 16.8601L2.05078 22.0001L7.30078 20.6201C8.75078 21.4101 10.3808 21.8301 12.0408 21.8301C17.5008 21.8301 21.9508 17.3801 21.9508 11.9201C21.9508 9.27005 20.9208 6.78005 19.0508 4.91005ZM12.0408 20.1501C10.5608 20.1501 9.11078 19.7501 7.84078 19.0001L7.54078 18.8201L4.42078 19.6401L5.25078 16.6001L5.05078 16.2901C4.22833 14.9771 3.79171 13.4593 3.79078 11.9101C3.79078 7.37005 7.49078 3.67005 12.0308 3.67005C14.2308 3.67005 16.3008 4.53005 17.8508 6.09005C18.6184 6.85392 19.2267 7.7626 19.6404 8.76338C20.0541 9.76417 20.265 10.8371 20.2608 11.9201C20.2808 16.4601 16.5808 20.1501 12.0408 20.1501ZM16.5608 13.9901C16.3108 13.8701 15.0908 13.2701 14.8708 13.1801C14.6408 13.1001 14.4808 13.0601 14.3108 13.3001C14.1408 13.5501 13.6708 14.1101 13.5308 14.2701C13.3908 14.4401 13.2408 14.4601 12.9908 14.3301C12.7408 14.2101 11.9408 13.9401 11.0008 13.1001C10.2608 12.4401 9.77078 11.6301 9.62078 11.3801C9.48078 11.1301 9.60078 11.0001 9.73078 10.8701C9.84078 10.7601 9.98078 10.5801 10.1008 10.4401C10.2208 10.3001 10.2708 10.1901 10.3508 10.0301C10.4308 9.86005 10.3908 9.72005 10.3308 9.60005C10.2708 9.48005 9.77078 8.26005 9.57078 7.76005C9.37078 7.28005 9.16078 7.34005 9.01078 7.33005H8.53078C8.36078 7.33005 8.10078 7.39005 7.87078 7.64005C7.65078 7.89005 7.01078 8.49005 7.01078 9.71005C7.01078 10.9301 7.90078 12.1101 8.02078 12.2701C8.14078 12.4401 9.77078 14.9401 12.2508 16.0101C12.8408 16.2701 13.3008 16.4201 13.6608 16.5301C14.2508 16.7201 14.7908 16.6901 15.2208 16.6301C15.7008 16.5601 16.6908 16.0301 16.8908 15.4501C17.1008 14.8701 17.1008 14.3801 17.0308 14.2701C16.9608 14.1601 16.8108 14.1101 16.5608 13.9901Z" />
									</svg>
									<a href="whatsapp://send?phone=+27640329224" class="text-slate-500 text-lg font-light hover:text-blue transition-all">WhatsApp</a>
								</div>
							</div>
							<div class="col-span-1 p-8 bg-slate-50 rounded-3xl">
								<div class="text-h3 font-medium text-digital-black mb-6">Billing</div>
								<div class="flex items-center gap-2 mb-4">
									<div class="material-symbols-outlined text-blue">mail</div>
									<a href="mailto:<EMAIL>" class="text-slate-500 text-lg font-light hover:text-blue transition-all"><EMAIL></a>
								</div>
								<div class="flex items-center gap-2 mb-4">
									<div class="material-symbols-outlined text-blue">call</div>
									<a href="tel:0212860123" class="text-slate-500 text-lg font-light hover:text-blue transition-all">************</a>
								</div>
								<div class="flex items-center gap-2">
									<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path class="fill-blue" d="M19.0508 4.91005C18.1338 3.98416 17.0418 3.25002 15.8383 2.75042C14.6348 2.25081 13.3439 1.99574 12.0408 2.00005C6.58078 2.00005 2.13078 6.45005 2.13078 11.9101C2.13078 13.6601 2.59078 15.3601 3.45078 16.8601L2.05078 22.0001L7.30078 20.6201C8.75078 21.4101 10.3808 21.8301 12.0408 21.8301C17.5008 21.8301 21.9508 17.3801 21.9508 11.9201C21.9508 9.27005 20.9208 6.78005 19.0508 4.91005ZM12.0408 20.1501C10.5608 20.1501 9.11078 19.7501 7.84078 19.0001L7.54078 18.8201L4.42078 19.6401L5.25078 16.6001L5.05078 16.2901C4.22833 14.9771 3.79171 13.4593 3.79078 11.9101C3.79078 7.37005 7.49078 3.67005 12.0308 3.67005C14.2308 3.67005 16.3008 4.53005 17.8508 6.09005C18.6184 6.85392 19.2267 7.7626 19.6404 8.76338C20.0541 9.76417 20.265 10.8371 20.2608 11.9201C20.2808 16.4601 16.5808 20.1501 12.0408 20.1501ZM16.5608 13.9901C16.3108 13.8701 15.0908 13.2701 14.8708 13.1801C14.6408 13.1001 14.4808 13.0601 14.3108 13.3001C14.1408 13.5501 13.6708 14.1101 13.5308 14.2701C13.3908 14.4401 13.2408 14.4601 12.9908 14.3301C12.7408 14.2101 11.9408 13.9401 11.0008 13.1001C10.2608 12.4401 9.77078 11.6301 9.62078 11.3801C9.48078 11.1301 9.60078 11.0001 9.73078 10.8701C9.84078 10.7601 9.98078 10.5801 10.1008 10.4401C10.2208 10.3001 10.2708 10.1901 10.3508 10.0301C10.4308 9.86005 10.3908 9.72005 10.3308 9.60005C10.2708 9.48005 9.77078 8.26005 9.57078 7.76005C9.37078 7.28005 9.16078 7.34005 9.01078 7.33005H8.53078C8.36078 7.33005 8.10078 7.39005 7.87078 7.64005C7.65078 7.89005 7.01078 8.49005 7.01078 9.71005C7.01078 10.9301 7.90078 12.1101 8.02078 12.2701C8.14078 12.4401 9.77078 14.9401 12.2508 16.0101C12.8408 16.2701 13.3008 16.4201 13.6608 16.5301C14.2508 16.7201 14.7908 16.6901 15.2208 16.6301C15.7008 16.5601 16.6908 16.0301 16.8908 15.4501C17.1008 14.8701 17.1008 14.3801 17.0308 14.2701C16.9608 14.1601 16.8108 14.1101 16.5608 13.9901Z" />
									</svg>
									<a href="whatsapp://send?phone=+27640329224" class="text-slate-500 text-lg font-light hover:text-blue transition-all">WhatsApp</a>
								</div>
							</div>
							<div class="col-span-1 p-8 bg-slate-50 rounded-3xl">
								<div class="text-h3 font-medium text-digital-black mb-6">Hosting</div>
								<div class="flex items-center gap-2 mb-4">
									<div class="material-symbols-outlined text-blue">mail</div>
									<a href="mailto:<EMAIL>" class="text-slate-500 text-lg font-light hover:text-blue transition-all"><EMAIL></a>
								</div>
								<div class="flex items-center gap-2 mb-4">
									<div class="material-symbols-outlined text-blue">call</div>
									<a href="tel:0212860123" class="text-slate-500 text-lg font-light hover:text-blue transition-all">************</a>
								</div>
								<div class="flex items-center gap-2">
									<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path class="fill-blue" d="M19.0508 4.91005C18.1338 3.98416 17.0418 3.25002 15.8383 2.75042C14.6348 2.25081 13.3439 1.99574 12.0408 2.00005C6.58078 2.00005 2.13078 6.45005 2.13078 11.9101C2.13078 13.6601 2.59078 15.3601 3.45078 16.8601L2.05078 22.0001L7.30078 20.6201C8.75078 21.4101 10.3808 21.8301 12.0408 21.8301C17.5008 21.8301 21.9508 17.3801 21.9508 11.9201C21.9508 9.27005 20.9208 6.78005 19.0508 4.91005ZM12.0408 20.1501C10.5608 20.1501 9.11078 19.7501 7.84078 19.0001L7.54078 18.8201L4.42078 19.6401L5.25078 16.6001L5.05078 16.2901C4.22833 14.9771 3.79171 13.4593 3.79078 11.9101C3.79078 7.37005 7.49078 3.67005 12.0308 3.67005C14.2308 3.67005 16.3008 4.53005 17.8508 6.09005C18.6184 6.85392 19.2267 7.7626 19.6404 8.76338C20.0541 9.76417 20.265 10.8371 20.2608 11.9201C20.2808 16.4601 16.5808 20.1501 12.0408 20.1501ZM16.5608 13.9901C16.3108 13.8701 15.0908 13.2701 14.8708 13.1801C14.6408 13.1001 14.4808 13.0601 14.3108 13.3001C14.1408 13.5501 13.6708 14.1101 13.5308 14.2701C13.3908 14.4401 13.2408 14.4601 12.9908 14.3301C12.7408 14.2101 11.9408 13.9401 11.0008 13.1001C10.2608 12.4401 9.77078 11.6301 9.62078 11.3801C9.48078 11.1301 9.60078 11.0001 9.73078 10.8701C9.84078 10.7601 9.98078 10.5801 10.1008 10.4401C10.2208 10.3001 10.2708 10.1901 10.3508 10.0301C10.4308 9.86005 10.3908 9.72005 10.3308 9.60005C10.2708 9.48005 9.77078 8.26005 9.57078 7.76005C9.37078 7.28005 9.16078 7.34005 9.01078 7.33005H8.53078C8.36078 7.33005 8.10078 7.39005 7.87078 7.64005C7.65078 7.89005 7.01078 8.49005 7.01078 9.71005C7.01078 10.9301 7.90078 12.1101 8.02078 12.2701C8.14078 12.4401 9.77078 14.9401 12.2508 16.0101C12.8408 16.2701 13.3008 16.4201 13.6608 16.5301C14.2508 16.7201 14.7908 16.6901 15.2208 16.6301C15.7008 16.5601 16.6908 16.0301 16.8908 15.4501C17.1008 14.8701 17.1008 14.3801 17.0308 14.2701C16.9608 14.1601 16.8108 14.1101 16.5608 13.9901Z" />
									</svg>
									<a href="whatsapp://send?phone=+27640329224" class="text-slate-500 text-lg font-light hover:text-blue transition-all">WhatsApp</a>
								</div>
							</div>
							<div class="col-span-1 p-8 bg-slate-50 rounded-3xl">
								<div class="text-h3 font-medium text-digital-black mb-6">Sales Queries</div>
								<div class="flex items-center gap-2 mb-4">
									<div class="material-symbols-outlined text-blue">mail</div>
									<a href="mailto:<EMAIL>" class="text-slate-500 text-lg font-light hover:text-blue transition-all"><EMAIL></a>
								</div>
								<div class="flex items-center gap-2">
									<div class="material-symbols-outlined text-blue">call</div>
									<a href="tel:0212860123" class="text-slate-500 text-lg font-light hover:text-blue transition-all">************</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="bg-slate-900 py-20 md:py-24 bg-center bg-cover bg-[url('../assets/backgrounds/wave-bg-1.svg')]">
			<div class="container">
				<div class="text-h1 text-white text-center mb-8">Cybersmart Offices</div>
				<p class="text-white mb-12 text-center max-w-[794px] mx-auto">72 Canterbury St, District Six, Cape Town, 7530</p>
				<div class="bg-center bg-cover bg-[url('../assets/feasibility-search-bg.jpg')] rounded-xl shadow-inner overflow-hidden">
					<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3310.461832387063!2d18.420923377028757!3d-33.92924797320349!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1dcc677c5bdc8d67%3A0x3e2cd4a1a8fae78e!2sCYBERSMART!5e0!3m2!1sen!2sza!4v1722515336035!5m2!1sen!2sza" width="100%" height="450" style="border:0;" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref } from 'vue';
import { useVuelidate } from '@vuelidate/core'
import { required, email } from '@vuelidate/validators'
import { useSubmitForm } from '@/composables/submitForm';
import FeasibilitySearch from '@/blocks/FeasibilitySearch.vue';


interface FormData {
	query: string;
	name: string;
	email: string;
	phone: string;
	company: string;
	message: string;
	faultType: string;
	fibreProvider: string;
	hostingFault: string;
	SupportQuery: string;
	BillingQuery: string;
	adslUsername: string;
	cyberSmartAccountNumber: string;
	locationOfService: string;
	applicableDomainName: string;
	applicableIpAddress: string;
	address: string;
	cslOrderReferenceNumber: string;
}

const formData = reactive<FormData>({
	query: '',
	name: '',
	email: '',
	phone: '',
	company: '',
	message: '',
	faultType: '',
	fibreProvider: '',
	hostingFault: '',
	SupportQuery: '',
	BillingQuery: '',
	adslUsername: '',
	cyberSmartAccountNumber: '',
	locationOfService: '',
	applicableDomainName: '',
	applicableIpAddress: '',
	address: '',
	cslOrderReferenceNumber: '',
});

const rules = {
	query: { required },
	name: { required },
	email: { required, email },
	phone: { required },
	message: { required },
}

const queryOptions = [
	{ label: 'Fault Reporting', value: 'Fault_Reporting' },
	{ label: 'Support Query', value: 'Support_Query' },
	{ label: 'Installation Status Query', value: 'Installation_Status_Query' },
	{ label: 'Billing Query', value: 'Billing_Query' },
];

const faultOptions = [
	{ label: 'ADSL Fault Reporting', value: 'ADSL_Fault_Reporting' },
	{ label: 'Fibre Fault Reporting', value: 'Fibre_Fault_Reporting' },
	{ label: 'Hosting Fault Reporting', value: 'Hosting_Fault_Reporting' },
	{ label: 'VOIP Fault Reporting', value: 'VOIP_Fault_Reporting' },
];

const billingQueryOptions = [
	{ label: 'ADSL Billing Query', value: 'ADSL_Billing_Query' },
	{ label: 'Fibre Billing Query', value: 'Fibre_Billing_Query' },
	{ label: 'Hosting Billing Query', value: 'Hosting_Billing_Query' },
	{ label: 'VOIP Billing Query', value: 'VOIP_Billing_Query' },
];

const fibreProviderOptions = [
	{ label: 'Lightspeed', value: 'Lightspeed' },
	{ label: 'Vumatel', value: 'Vumatel' },
	{ label: 'Openserve', value: 'Openserve' },
	{ label: 'SADV', value: 'SADV' },
	{ label: 'Century City Connect', value: 'Century_City_Connect' },
];

const hostingFaultOptions = [
	{ label: 'Domain Fault Query', value: 'Domain_Fault_Query' },
	{ label: 'Email Fault Query', value: 'Email_Fault_Query' },
	{ label: 'Website Fault Query', value: 'Website_Fault_Query' },
	{ label: 'Cloud Hosting Fault Query', value: 'Cloud_Hosting_Fault_Query' },
];

const hostingSupportOptions = [
	{ label: 'Domain Support Query', value: 'Domain_Support_Query' },
	{ label: 'Email Support Query', value: 'Email_Support_Query' },
	{ label: 'Website Support Query', value: 'Website_Support_Query' },
	{ label: 'Cloud Hosting Support Query', value: 'Cloud_Hosting_Support_Query' },
];

const hostingBillingOptions = [
	{ label: 'Domain', value: 'Domain_Billing_Query' },
	{ label: 'Email', value: 'Email_Billing_Query' },
	{ label: 'Cloud Hosting', value: 'Cloud_Hosting_Billing_Query' },
];

const supportQueryOptions = [
	{ label: 'ADSL Support Query', value: 'ADSL_Support_Query' },
	{ label: 'Fibre Support Query', value: 'Fibre_Support_Query' },
	{ label: 'Hosting Support Query', value: 'Hosting_Support_Query' },
	{ label: 'VOIP Support Query', value: 'VOIP_Support_Query' },
];

// Initialize Vuelidate
const v$ = useVuelidate(rules, formData)

// Initialize the composable and extract its properties and methods
const { isSubmitting, error, success, submitForm } = useSubmitForm('Contact Us', formData)

const messageLabel = computed(() => {
	var label = 'Message'
	switch (formData.query) {
		case 'Fault_Reporting':
			label = 'Please describe the fault you are encountering:'
			break
		case 'Support_Query':
			label = 'Please describe the support issue with which you require assistance:'
			break
		case 'Installation_Status_Query':
			label = 'Please provide us with details of your query including the date on which you ordered the service:'
			break
		case 'Billing_Query':
			label = 'Please describe the billing query with which you require assistance:'
			break
		default:
			break
	}
	return label
})

const resetForm = () => {
	formData.query = ''
	formData.name = ''
	formData.email = ''
	formData.phone = ''
	formData.company = ''
	formData.message = ''
	formData.faultType = ''
	formData.fibreProvider = ''
	formData.hostingFault = ''
	formData.SupportQuery = ''
	formData.BillingQuery = ''
	formData.adslUsername = ''
	formData.cyberSmartAccountNumber = ''
	formData.locationOfService = ''
	formData.applicableDomainName = ''
	formData.applicableIpAddress = ''
	formData.address = ''
	formData.cslOrderReferenceNumber = ''
	v$.value.$reset()
};

const validateAndSubmit = async () => {
	try {
		// If validation passes, submit the form
		if (!v$.value.$invalid) {
			// First, try to create the contact in Brevo
			// await createContact(formData);

			// If Brevo creation succeeds, submit the form normally
			await submitForm();

			// If submission is successful, reset the form
			if (success.value) {
				resetForm();
			}
		}
	} catch (err: unknown) {
		console.error('Form submission error:', err);
		// Type guard to check if err is an Error object
		if (err instanceof Error) {
			error.value = err.message;
		} else {
			error.value = 'Failed to submit form. Please try again.';
		}
	}
};
</script>