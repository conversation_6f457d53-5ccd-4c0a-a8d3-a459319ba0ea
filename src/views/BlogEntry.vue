<template>
	<div>
		<FeasibilitySearch 
			subtitle="Find your fibre"
			title="Search available connectivity"
			description="Enter your address below to find the best fibre options available to you."
		/>
		<div class="bg-slate-50 pt-[200px] pb-20 bg-center bg-no-repeat bg-cover bg-[url('../assets/backgrounds/wave-bg-5-blog-hero.svg')] -mt-[177px]">
			<div class="container flex max-md:flex-col justify-between items-center gap-12 md:gap-16">
				<div class="order-1 max-w-[800px]">
					<DsBreadcrumbs
						:breadcrumbs="breadCrumbTrail"
						class="mb-8 md:mb-20 block"
					/>
					<DsBadge type="accent" class="mb-8">{{ details.category }}</DsBadge>
					<h1 class="text-display text-digital-black font-semibold mb-11">{{ details.title }}</h1>
					<p class="text-slate-500 font-light">{{ details.synopsis }}</p>
				</div>
				<div class="order-0 md:order-2">
					<img class="w-auto h-full object-cover rounded-3xl" :src="details.feature" :alt="details.title">
				</div>
			</div>
		</div>
		<div class="container my-20 md:my-24">
			<div class="mx-auto max-w-[768px] prose" v-html="details.content"></div>
		</div>
	</div>
	
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import marked from 'marked';
import { useStore } from 'vuex'
import { useHead } from '@unhead/vue'

interface PageAttributes {
	seo: {
		metaTitle: string;
		metaDescription: string;
	};
}

const breadCrumbTrail = [
	{
		label: 'Resources',
		url: '#'
	},
	{
		label: 'Blog',
		url: '/blog'
	}
]

const props = defineProps<{ id: string }>();

const details = ref({
	title: '',
	category_id: '',
	category: '',
	revealDate: '',
	content: '',
	feature: '',
	synopsis: '',
});

const posts = ref<Array<{
	id: string;
	revealDate: string;
	category: string;
	categoryId: string;
	feature: string;
	synopsis: string;
	title: string;
}>>([]);

const router = useRouter();
const store = useStore()

const goBack = () => {
	router.go(-1);
};

onMounted(async () => {
	store.commit('setDarkMenu', true)

	const backendUrl = import.meta.env.VITE_BACKEND_URL;

	const detailsResponse = await fetch(`${import.meta.env.VITE_BACKEND_URL}blogs/${props.id}`);
	const detailsData = await detailsResponse.json();

	details.value = {
		title: detailsData.title,
		category_id: detailsData.category.id,
		category: detailsData.category.name,
		revealDate: new Date(detailsData.revealDate).toLocaleDateString('en-US', {
			month: 'short',
			day: 'numeric',
			year: 'numeric'
		}),
		content: marked(detailsData.content),
		feature: `${import.meta.env.VITE_BACKEND_BASE_URL}${detailsData.feature.url}`,
		synopsis: detailsData.synopsis
	};

	useHead({
		title: detailsData.metaTitle,
		meta: [
			{
				name: 'description',
				content: detailsData.metaDescription,
			},
		],
	});

	const relatedPostsResponse = await fetch(
		`${backendUrl}blogs/by-category?category.id=${detailsData.category.id}&id_ne=${detailsData.id}`
	);
	const relatedPostsData = await relatedPostsResponse.json();

	relatedPostsData.forEach((item: any) => {
		posts.value.push({
			id: item.id,
			revealDate: new Date(item.revealDate).toLocaleDateString('en-US', {
				month: 'short',
				day: 'numeric',
				year: 'numeric'
			}),
			category: item.category?.name,
			categoryId: item.category?.id,
			feature: `${backendUrl}${item.feature.url}`,
			synopsis: item.synopsis,
			title: item.title
		});
	});
});
</script>

<style lang="scss"></style>
