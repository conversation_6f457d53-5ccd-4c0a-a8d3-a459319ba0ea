<template>
	<div class="container py-10">
		<h1 class="text-h1 text-digital-black font-semibold mb-9">THANK YOU FOR YOUR FIBRE ORDER</h1>
		<p class="text-slate-500 font-light mb-6">We have received your order and it is being processed.</p>
		<p class="text-slate-500 font-light mb-6">You will receive an updated order number within one business day and our team will be in touch to confirm your details and schedule a time and date for installation.</p>
		<p v-if="orderNumber" class="text-blue font-semibold mb-6">Your reference number is {{ orderNumber }}</p>
		<p class="text-slate-500 font-light mb-9">Should you wish to follow up on your order you can contact our call centre on {{ phoneNumber }}</p>
		<LeaveReview />
		<div class="mt-9">
			<p class="text-slate-500 font-light mb-6">We're Social, Follow Us!</p>
			<div class="flex gap-4 items-center">
				<a target="_blank" :href="facebookLink">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
						<g clip-path="url(#clip0_4863_2138)">
							<path
								d="M24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 17.9895 4.3882 22.954 10.125 23.8542V15.4688H7.07812V12H10.125V9.35625C10.125 6.34875 11.9166 4.6875 14.6576 4.6875C15.9701 4.6875 17.3438 4.92188 17.3438 4.92188V7.875H15.8306C14.34 7.875 13.875 8.80008 13.875 9.75V12H17.2031L16.6711 15.4688H13.875V23.8542C19.6118 22.954 24 17.9895 24 12Z"
								fill="#94A3B8" />
						</g>
						<defs>
							<clipPath id="clip0_4863_2138">
								<rect width="24" height="24" fill="white" />
							</clipPath>
						</defs>
					</svg>
				</a>
				<a v-if="showLinkedin" target="_blank" :href="linkedinLink">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
						<path
							d="M22.2234 0H1.77187C0.792187 0 0 0.773438 0 1.72969V22.2656C0 23.2219 0.792187 24 1.77187 24H22.2234C23.2031 24 24 23.2219 24 22.2703V1.72969C24 0.773438 23.2031 0 22.2234 0ZM7.12031 20.4516H3.55781V8.99531H7.12031V20.4516ZM5.33906 7.43438C4.19531 7.43438 3.27188 6.51094 3.27188 5.37187C3.27188 4.23281 4.19531 3.30937 5.33906 3.30937C6.47813 3.30937 7.40156 4.23281 7.40156 5.37187C7.40156 6.50625 6.47813 7.43438 5.33906 7.43438ZM20.4516 20.4516H16.8937V14.8828C16.8937 13.5562 16.8703 11.8453 15.0422 11.8453C13.1906 11.8453 12.9094 13.2937 12.9094 14.7891V20.4516H9.35625V8.99531H12.7687V10.5609H12.8156C13.2891 9.66094 14.4516 8.70938 16.1813 8.70938C19.7859 8.70938 20.4516 11.0813 20.4516 14.1656V20.4516Z"
							fill="#94A3B8" />
					</svg>
				</a>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import store from '@/store';
import brand from '@/brand';
import LeaveReview from '@/components/LeaveReview.vue';

// Reactive reference for orderNumber
const orderNumber = store.state.orderNumber;

// Computed properties
const showLinkedin = computed(() => {
	const show = {
		cybersmart: true,
		ftta: true,
		gigalight: false,
	};
	return show[brand.getBrand()];
});

const linkedinLink = computed(() => {
	const links = {
		cybersmart: 'https://www.linkedin.com/company/cybersmart-pty-ltd',
		ftta: 'https://www.linkedin.com/company/fttasouthafrica/',
		gigalight: '',
	};
	return links[brand.getBrand()];
});

const facebookLink = computed(() => {
	const links = {
		cybersmart: 'https://www.facebook.com/CybersmartSA',
		ftta: 'https://www.facebook.com/Fibre-to-the-Apartment-683208105385551/',
		gigalight: 'https://www.facebook.com/gigalight.fibre',
	};
	return links[brand.getBrand()];
});

const phoneNumber = computed(() => {
	const phones = {
		cybersmart: '021 | 031 | ************',
		ftta: '************',
		gigalight: '************',
	};
	return phones[brand.getBrand()];
});
</script>