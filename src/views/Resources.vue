<template>
	<div v-if="page">
		<FeasibilitySearch 
			subtitle="Find your fibre"
			title="Search available connectivity"
			description="Enter your address below to find the best fibre options available to you."
		/>
		<div class="pt-20 md:pt-24 pb-16">
			<div class="container max-w-[668px]">
				<DsSectionIntro class="mb-12" :subtitle="page.attributes.subtitle" :title="page.attributes.title"
					:description="page.attributes.description"></DsSectionIntro>
				<DsSearchInput name="resourceSearch" placeholder="Search" v-model="resourceSearch" />
			</div>
		</div>
		<div class="py-20 md:py-24 bg-slate-50 mb-20 md:mb-24">
			<div v-if="filteredResources.length > 0" class="container flex flex-col gap-16">
				<div v-for="resourceCategory in filteredResources" :key="resourceCategory.id"
					class="flex flex-col gap-6">
					<DsIconBlock :icon="resourceCategory.attributes.icon" />
					<div class="text-h1 text-digital-black font-semibold">{{ resourceCategory.attributes.title }}</div>
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-4 gap-x-10">
						<a v-for="download in resourceCategory.attributes.resource_items.data" :key="download.id"
							:href="backEndUrl(download.attributes.file.data.attributes.url)" target="_blank"
							class="flex gap-2 items-center text-slate-500 text-lg font-light hover:text-digital-black transition-all">
							<span class="shrink-0">
								<svg width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M25.375 8.15625V25.375C25.375 26.3364 24.9931 27.2584 24.3133 27.9383C23.6334 28.6181 22.7114 29 21.75 29H19.9375V27.1875H21.75C22.2307 27.1875 22.6917 26.9965 23.0316 26.6566C23.3715 26.3167 23.5625 25.8557 23.5625 25.375V8.15625H19.9375C19.2164 8.15625 18.5249 7.86981 18.0151 7.35995C17.5052 6.85008 17.2188 6.15856 17.2188 5.4375V1.8125H7.25C6.7693 1.8125 6.30828 2.00346 5.96837 2.34337C5.62846 2.68328 5.4375 3.14429 5.4375 3.625V19.9375H3.625V3.625C3.625 2.66359 4.00692 1.74156 4.68674 1.06174C5.36656 0.381919 6.28859 0 7.25 0L17.2188 0L25.375 8.15625ZM2.9 21.4781H0V28.7263H1.43369V26.2939H2.88912C3.40931 26.2939 3.85156 26.1906 4.21588 25.9804C4.58381 25.7683 4.86475 25.4819 5.05506 25.1213C5.2526 24.7428 5.35288 24.3211 5.34688 23.8942C5.34688 23.4411 5.25081 23.0314 5.0605 22.6671C4.8712 22.305 4.5818 22.0049 4.22675 21.8026C3.86425 21.5851 3.42381 21.4781 2.9 21.4781ZM3.88781 23.8942C3.89435 24.133 3.8414 24.3697 3.73375 24.5829C3.63717 24.7686 3.48683 24.9209 3.30238 25.0198C3.09138 25.1241 2.858 25.1752 2.62269 25.1684H1.42825V22.62H2.6245C3.01962 22.62 3.32956 22.7288 3.5525 22.9481C3.77544 23.1692 3.88781 23.4846 3.88781 23.8942ZM6.09363 21.4781V28.7263H8.73987C9.46669 28.7263 10.0703 28.5813 10.5487 28.2968C11.033 28.007 11.4111 27.5692 11.6272 27.0479C11.8628 26.5042 11.9824 25.8481 11.9824 25.0832C11.9824 24.3219 11.8646 23.6731 11.6272 23.1348C11.4136 22.6195 11.0391 22.1871 10.5596 21.9022C10.0811 21.6195 9.47394 21.4781 8.73806 21.4781H6.09363ZM7.52731 22.6472H8.54775C8.99725 22.6472 9.36337 22.7378 9.65156 22.9227C9.95069 23.1183 10.1764 23.4078 10.2932 23.7456C10.4364 24.1099 10.5071 24.5648 10.5071 25.1104C10.5127 25.4719 10.4713 25.8326 10.3838 26.1834C10.3192 26.46 10.1983 26.7204 10.0286 26.9482C9.87086 27.1515 9.66192 27.3091 9.42319 27.405C9.14345 27.5097 8.84635 27.5601 8.54775 27.5536H7.52731V22.6472ZM14.3115 25.8426V28.7263H12.8796V21.4781H17.4979V22.6617H14.3115V24.6863H17.2224V25.8426H14.3115Z" fill="#209FD6"/>
								</svg>
							</span>
							{{ download.attributes.label }}
						</a>
					</div>
				</div>
			</div>
			<div v-else class="container">
				<div class="text-h1 text-digital-black font-semibold">No downloads found meeting your criteria.</div>
			</div>
		</div>
		<component v-for="block in page.attributes.Content" v-bind="block" :key="block.id"
			:is="useGetComponent(block.__component)" />
	</div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useFetchSinglePage, useGetComponent } from '@/composables/fetchSinglePage'
import { useFetchResources } from '@/composables/fetchResources'
import FeasibilitySearch from '@/blocks/FeasibilitySearch.vue';

const { page } = useFetchSinglePage('resource')
const { resources } = useFetchResources()

const resourceSearch = ref<string>('');

const filteredResources = computed(() => {
	if (resourceSearch.value.length > 3) {
		return resources.value.data
			.map(category => {
				const filteredItems = category.attributes.resource_items.data.filter(item =>
					item.attributes.label.toLowerCase().includes(resourceSearch.value.toLowerCase())
				);
				return {
					...category,
					attributes: {
						...category.attributes,
						resource_items: {
							data: filteredItems
						}
					}
				};
			})
			.filter(category => category.attributes.resource_items.data.length > 0);
	} else {
		return resources.value ? resources.value.data : [];
	}
});

const backEndUrl = (url: string) => {
	return import.meta.env.VITE_BACKEND_BASE_URL + url
}
</script>