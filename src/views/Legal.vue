<template>
	<div class="legal has-text-left columns">
		<div class="column is-8">
			<div class="legal-questions">
				<h2 class="title is-block-desktop-only is-hidden-mobile">Legal</h2>

				<hr class="is-block-tablet is-hidden-desktop">
			</div>
		</div>
		<div class="column is-4">
			<div class="legal-contact-us">
				<div class="legal-contact-box box">
					<h2 class="title">Can't find the answer you're looking for?</h2>
					<a class="button is-primary legal-button is-block-mobile is-inline-block-desktop"
						href="/contact-us">CONTACT US</a>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	components: {},
	data() {
		return {}
	},
	computed: {
		results() {
			return this.$store.getters.getLegal
		},
		legals() {
			return this.results.reduce((r, v) => {
				(r[v.category] || (r[v.category] = [])).push(v)
				return r
			}, {})
		},
		activeLegal() {
			return this.results.length > 0 ? this.results[0].id : null
		},
		lookup() {
			return this.results.reduce((r, v) => {
				r[v.id] = v
				return r
			}, {})
		}
	},
	async mounted() {
		if (!this.results) {
			this.$store.dispatch('fetchLegal')
		}
	},
	methods: {
		isEmpty(value) {
			if (value == null) {
				return true
			}
			if (Array.isArray(value) || typeof value === 'string') {
				return value.length === 0
			}
			if (typeof value === 'object') {
				return Object.keys(value).length === 0
			}
			return false
		}
	}
}
</script>