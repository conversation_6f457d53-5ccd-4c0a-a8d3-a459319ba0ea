<template>
	<div>
		<Loader :loading="loading" :message="loaderMessage" :retry="loadeRetry" @load-retry="loadRetry" />
		<form class="container grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 my-9" @submit.prevent="">
			<div class="lg:col-span-2">
				<h1 class="text-h1 text-digital-black font-semibold mb-9">Order Details:</h1>
				<div v-if="product" class="mb-9">
					<div class="flex max-lg:flex-col justify-between gap-8 lg:gap-10">
						<div>
							<div class="text-h3 text-digital-black font-semibold mb-2" v-if="!zone.preOrder">{{ zone.provider_name }}</div>
							<div class="text-h3 text-digital-black font-semibold mb-2" v-else>{{ zone.provider_name + " Pre-Order" }}</div>
							<p class="text-slate-500 font-light mb-8">{{ product.name }}</p>
							<DsFeatureList v-if="productDetails" :feature-items="formatProductFeatures(product.details)" />
							<p v-else class="text-slate-500 font-light">No details available for this product</p>
							<div class="text-h3 text-digital-black font-semibold mt-8 mb-2">Cost:</div>
							<div class="text-subtitle text-digital-black">R{{ product.monthly }} INCL. VAT Monthly fee</div>
							<div v-if="product.setup == '0.00'" class="text-subtitle text-digital-black"><span class="font-semibold">Free</span> Activation</div>
							<div v-else class="text-subtitle text-digital-black"><span class="font-semibold">R{{ product.setup }} INCL. VAT</span> Once off setup fee</div>
						</div>
						<div class="">
							<div class="text-h3 text-digital-black font-semibold mb-2">Address:</div>
							<p class="text-slate-500 font-light mb-8">{{ builtAddress }}</p>
							<DsButton
								label="Change address"
								type="outline"
								@click="$router.push('/feasibility-search')"
								class="mb-8"
							/>
						</div>
					</div>
				</div>
				<div v-if="orderResponse" class="">
					<DsButton
						label="Delete"
						icon-before="delete"
						type="secondary"
						@click.prevent="orderResponse = null"
					/>
					<div v-if="orderResponse.errors">
						<div class="text-h3 text-digital-black font-semibold mb-6">Errors</div>
						<div v-for="(error, key) in orderResponse.errors" :key="key" class="mb-2">
							<span class="font-semibold">{{ key }}:</span> {{ error }}
						</div>
					</div>
					<p class="text-slate-500 font-light" v-else-if="orderResponse.message">{{ orderResponse.message }}</p>
					<p class="text-slate-500 font-light" v-else-if="typeof orderResponse == 'string'">{{ orderResponse }}</p>
				</div>
				<div class="flex flex-col gap-9">
					<div>
						<p class="text-red-500 mb-6" v-if="secretCodeError">{{ secretCodeError }}</p>
						<p class="text-red-500 mb-6" v-if="sendButtonsError">{{ sendButtonsError }}</p>
					</div>
		
					<div class="">
						<div class="text-h3 text-digital-black font-semibold mb-2">Cell Number used for WhatsApp</div>
						<p class="text-slate-500 font-light mb-6">Enter your cellphone number below to continue.</p>
						<div class="flex items-center gap-5">
							<div class="">
								<VueTelInput
									class="!border-none !gap-2 focus-within:!shadow-none focus-within:!border-none group"
									:disabled="disabled.cellphone"
									v-model="customer.cellphone"
									:defaultCountry="phoneOptions.defaultCountry"
									:inputOptions="phoneOptions"
									:autoFormat="false"
									@on-input="cellphoneValidate"
									:preferredCountries="phoneOptions.preferredCountries"
									mode="international"
									ref="cellphoneInput"
									:dropdownOptions="{ showDialCodeInSelection: true, showFlags: true, showDialCodeInList: true, disabled: false }" />
							</div>
							<div class="" v-if="show.throbberCellPhone">
								<img src="../assets/throbber.svg" alt="loading" />
							</div>
						</div>
						<p v-if="cellphoneError" class="text-red-500 font-light" >{{ cellphoneError }}</p>
						<p v-if="show.createContactMessage" class="text-subtitle text-digital-black font-semibold">
							You have been granted access with the right to create a {{ authType[mesAuthAndAddMasterAccount] ? 'MASTER' : 'SUB' }} ACCOUNT CONTACT. Please fill in the details
						</p>
					</div>
					<div class="" v-if="show.billingCode">
						<div class="text-h3 text-digital-black font-semibold mb-2">Billing Code</div>
						<div class="flex items-center gap-5">
							<DsTextInput
								label="Billing Code (reference on your invoice)"
								name="billingCode"
								v-model="billingCode"
								:invalid="firstNameError !== ''"
								:invalid-message="firstNameError"
								:disabled="disabled.billingCode"
							/>
							<div class="send__icon" v-if="show.throbberBillingCode">
								<img src="../assets/throbber.svg" alt="loading" />
							</div>
						</div>
		
						<DsButton
							v-if="show.checkBillingCode"
							label="Continue"
							type="primary"
							@click="billingCodeValidate()"
							class="mt-6"
						/>
					</div>
					<p class="text-slate-500 font-light" v-if="subAccountAddText">{{ subAccountAddText }}</p>
					<div class="" v-if="show.accountsList">
						<div class="text-h3 text-digital-black font-semibold mb-2">Choose account</div>
						<DsSelectInput
							label="Select Account"
							v-model="userAccount"
							:options="accountsList"
							:disabled="disabled.accountsList"
							@change="accountListSelected()"
						/>
					</div>
					<div class="" v-if="show.secretCode">
						<div class="text-h3 text-digital-black font-semibold mb-2">Secret Code</div>
						<span class="flex items-center gap-5">
							<DsTextInput
								label="Your secret code"
								name="secretCode"
								v-model="secretCode"
								:invalid="firstNameError !== ''"
								note="Your secret code is a 6 digit code sent to account holder cel"
							/>
							<div class="send__icon" v-if="show.throbberSecretCode">
								<img src="../assets/throbber.svg" alt="loading" />
							</div>
						</span>
					</div>
					<div class="" v-if="show.maskedInformation">
						<p class="text-digital-black font-medium">Secret code was sent to cell phone ending with {{ primaryMaskedCellphone }} and email address starting with {{ primaryMaskedEmail }}</p>
					</div>
					<div class="">
						<p class="text-digital-black font-medium mb-3" v-if="show.sendingInterval">{{ sendingIntervalCount }} before we can re-send pin</p>
						<p class="text-digital-black font-medium" v-if="show.billingCodePinMessage">We sent PIN to the primary account/ Please ask them for a PIN</p>
					</div>
					<div class="" v-if="show.viaButtons">
						<div class="flex flex-wrap gap-3">
							<DsButton
								label="Get Pin Via WhatsApp"
								type="primary"
								:disabled="disabled.WhatsAppButton"
								@click="getSecretCode(pinDeliveryChannel['WhatsApp'])"
							>
								<template v-slot:iconBefore>
									<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"><path fill="#231F20" d="M19.05 4.91A9.82 9.82 0 0 0 12.04 2c-5.46 0-9.91 4.45-9.91 9.91c0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21c5.46 0 9.91-4.45 9.91-9.91c0-2.65-1.03-5.14-2.9-7.01m-7.01 15.24c-1.48 0-2.93-.4-4.2-1.15l-.3-.18l-3.12.82l.83-3.04l-.2-.31a8.26 8.26 0 0 1-1.26-4.38c0-4.54 3.7-8.24 8.24-8.24c2.2 0 4.27.86 5.82 2.42a8.18 8.18 0 0 1 2.41 5.83c.02 4.54-3.68 8.23-8.22 8.23m4.52-6.16c-.25-.12-1.47-.72-1.69-.81c-.23-.08-.39-.12-.56.12c-.17.25-.64.81-.78.97c-.14.17-.29.19-.54.06c-.25-.12-1.05-.39-1.99-1.23c-.74-.66-1.23-1.47-1.38-1.72c-.14-.25-.02-.38.11-.51c.11-.11.25-.29.37-.43s.17-.25.25-.41c.08-.17.04-.31-.02-.43s-.56-1.34-.76-1.84c-.2-.48-.41-.42-.56-.43h-.48c-.17 0-.43.06-.66.31c-.22.25-.86.85-.86 2.07s.89 2.4 1.01 2.56c.12.17 1.75 2.67 4.23 3.74c.59.26 1.05.41 1.41.52c.59.19 1.13.16 1.56.1c.48-.07 1.47-.6 1.67-1.18c.21-.58.21-1.07.14-1.18s-.22-.16-.47-.28"></path></svg>
								</template>
							</DsButton>
							<DsButton
								:disabled="disabled.TGButton"
								label="Get Pin Via Telegram"
								type="primary"
								@click="getSecretCode(pinDeliveryChannel['TG'])"
							>
								<template v-slot:iconBefore>
									<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"><path fill="none" stroke="#231F20" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m11.985 15.408l3.242 3.686c1.2 1.365 1.801 2.048 2.43 1.881c.628-.166.844-1.064 1.275-2.861l2.39-9.968c.665-2.768.997-4.151.259-4.834s-2.017-.175-4.575.84L5.14 8.865c-2.046.813-3.069 1.219-3.134 1.917a1 1 0 0 0 0 .214c.063.699 1.084 1.108 3.128 1.927c.925.371 1.388.557 1.72.912q.056.06.108.124c.306.38.436.88.697 1.876l.489 1.867c.253.97.38 1.456.713 1.522s.622-.336 1.201-1.141zm0 0l-.317-.33c-.362-.378-.543-.566-.543-.8s.18-.423.543-.8l3.573-3.724" color="#000000"></path></svg>
								</template>
							</DsButton>
							<DsButton
								:disabled="disabled.SMSButton"
								label="Get Pin Via SMS"
								type="primary"
								icon-before="sms"
								@click="getSecretCode(pinDeliveryChannel['SMS'])"
							/>
						</div>
					</div>
					<div class="flex flex-col gap-6" v-if="show.dataInput">
						<div v-if="!authType[mesAuthAndAddMasterAccount] && !authType[mesAuthAndAddSubAccount]">
							<DsTextInput
								label="ID/Passport/Company Registration Number"
								name="idNumber"
								v-model="customer.idNumber"
								:invalid="idNumberError !== ''"
								:invalid-message="idNumberError"
							/>
							<DsTextInput
								v-if="customer.idNumber.indexOf('/') >= 0"
								label="Company Name"
								name="companyName"
								v-model="customer.companyName"
								:invalid="companyNameError !== ''"
								:invalid-message="companyNameError"
							/>
							<DsTextInput
								v-if="customer.idNumber.indexOf('/') >= 0"
								label="VAT Number"
								name="vatNumber"
								v-model="customer.vatNumber"
								:invalid="vatNumberError !== ''"
								:invalid-message="vatNumberError"
							/>
						</div>
						<DsSelectInput
							label="Select Title"
							v-model="customer.titleId"
							:options="titleIds"
							:invalid="titleIdError !== ''"
							:invalid-message="titleIdError"
						/>
						<DsTextInput
							label="First Name"
							name="firstName"
							v-model="customer.firstName"
							:invalid="firstNameError !== ''"
							:invalid-message="firstNameError"
						/>
						<DsTextInput
							label="Last Name"
							name="lastName"
							v-model="customer.lastName"
							:invalid="lastNameError !== ''"
							:invalid-message="lastNameError"
						/>
						<DsTextInput
							label="Email Address"
							name="email"
							v-model="customer.email"
							:invalid="emailError !== ''"
							:invalid-message="emailError"
						/>
						<DsTextInput
							v-if="show.dataInput && (!authType[mesAuthAndAddMasterAccount] && !authType[mesAuthAndAddSubAccount])"
							label="Daytime Contact Number"
							name="daytimeContactNumber"
							v-model="customer.daytimeContactNumber"
							:invalid="daytimeContactNumberError !== ''"
							:invalid-message="daytimeContactNumberError"
						/>
					</div>
					<div v-show="show.submitButtonAndCheckBoxConditions" class="">
						<p class="text-slate-500 font-light mb-2">You only pay once you're connected</p>
						<DsCheckbox
							:label="termsString"
							name="tandcs"
							v-model="tandcs"
							:invalid="tandcsError !== ''"
							:invalid-message="tandcsError"
							class="mb-2"
						/>
						<DsButton
							label="Continue"
							:disabled="disabled.confirmButton"
							@click="checkForm"
						/>
					</div>
				</div>
			</div>
			<div v-if="product">
				<div class="bg-gray-100 rounded-3xl sticky top-10">
					<div class="py-9 px-8 border-b border-slate-300">
						<div class="text-subtitle text-blue font-semibold flex items-center gap-2">
							<div class="material-symbols-outlined">check_circle</div>
							STEP 1: SELECT PACKAGE
						</div>
					</div>
					<div class="py-9 px-8 border-b border-slate-300">
						<div class="text-subtitle text-blue font-semibold flex items-center gap-2">
							<div class="material-symbols-outlined">check_circle</div>
							STEP 2: DETAILS
						</div>
					</div>
					<div class="py-9 px-8 text-subtitle text-blue font-semibold">STEP 3: CONFIRM</div>
				</div>
			</div>
		</form>
	</div>
</template>


<script>
import brand from "@/brand";
import Loader from "@/components/Loader.vue";
import store from "@/store";
import axios from "axios";
import _ from "lodash";
import moment from "moment";
import { VueTelInput } from 'vue-tel-input';
import 'vue-tel-input/vue-tel-input.css';

const PROVIDER_FEASIBILITY = "Provider Feasibility";


export default {
	name: "Purchase",
	components: {
		Loader,
		VueTelInput,
	},
	data() {
		return {
			mesAuthThis: "authorize this",
			mesAuthAndAddMasterAccount: "authorize and add as a masteaccount contact",
			mesAuthAndAddSubAccount: "authorize and add as a sub-account contact",
			phoneOptions: {
				placeholder: "Cellphone Number",
				defaultCountry: "ZA",
				preferredCountries: [
					"ZA",
					"RU",
					"GB"
				],
				required: true,
				validCharactersOnly: true,
				maxlength: 13,
				mask: {},
			},
			sendingIntervalCount: 60,
			loading: false,
			loaderMessage: null,
			loadeRetry: false,
			activate_cost: "",
			debounce: null,

			/* eslint-disable */
			numRegex: /^\d+$/,
			emailRegex: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
			compRegex: /^((19|20)[\d]{2}\/[\d]{6}\/[\d]{2})$/,
			passportRegex: /^(?!^0+$)[a-zA-Z0-9]{3,20}$/,
			firstLastNameRegex: /^[a-zA-Z' \-]+$/,
			/* eslint-enable */

			errors: [],
			customer: this.$store.getters.getCustomer,
			showCustomerDetails: true,
			secretCodeError: "",
			sendButtonsError: "",
			titleIdError: "",
			firstNameError: "",
			lastNameError: "",
			emailError: "",
			daytimeContactNumberError: "",
			cellphoneError: "",
			idNumberError: "",
			companyNameError: "",
			vatNumberError: "",
			streetAddressError: "",
			suburbError: "",
			provinceError: "",
			postalCodeError: "",
			tandcs: false,
			tandcsError: "",
			orderResponse: null,
			sendInt: null,
			sendCount: 0,
			accountsList: [],
			userAccount: [],
			accountStatusesInSelect: {
				"Active Account": "Active",
				"Suspended Account": "Suspended",
				"Inactive Account": "Inactive"
			},
			subAccountAddText: "",
			phoneValidate: "",
			billingCode: null,
			secretCode: "",
			show: {
				submitButtonAndCheckBoxConditions: false,
				secretCode: false,
				dataInput: false,
				viaButtons: false,
				throbberSecretCode: false,
				throbberCellPhone: false,
				throbberBillingCode: false,
				sendingInterval: false,
				accountsList: false,
				billingCode: false,
				checkBillingCode: true,
				billingCodePinMessage: false,
				createContactMessage: false,
				maskedInformation: false
			},
			disabled: {
				SMSButton: false,
				TGButton: false,
				WhatsAppButton: false,
				cellphone: false,
				confirmButton: true,
				secretCode: false,
				accountsList: false,
				billingCode: false
			},
			addressCode: store.state.addressCode
				? store.state.addressCode
				: undefined,
			addressId: store.state.addressId ? store.state.addressId : undefined,
			product: store.state.product ? store.state.product : undefined,
			productIds: store.state.productIds ? store.state.productIds : {},
			pinDeliveryChannel: {
				WhatsApp: "WhatsApp",
				TG: "TG",
				SMS: "SMS"
			},
			authType: {
				"authorize this": false,
				"authorize and add as a masteaccount contact": false,
				"authorize and add as a sub-account contact": false
			},
			primaryMaskedCellphone: "",
			primaryMaskedEmail: ""
		};
	},
	mounted() {
		if (this.product === undefined) {
			this.$router.push({ name: "home" });
		}

		this.getUpdatedProductDetails();
	},
	watch: {
		"secretCode": {
			async handler(code) {
				if (code) {
					if (this.show.secretCode && code.length === 6) {
						this.show.throbberSecretCode = true;
						this.secretCodeError = "";
						this.disabled.TGButton = true;
						this.disabled.SMSButton = true;
						this.disabled.WhatsAppButton = true;
						let requestData = {
							phoneNumber: this.phoneValidate,
							inputCode: code
						};
						console.log("TEST DATA BILLING CODE VIEW", this.billingCode);
						if (this.billingCode) {
							requestData.billingCode = this.billingCode;
						}
						try {
							const response = await axios.post(`${import.meta.env.VITE_AUTHSERVICE_URL}secretCodeValidate`, requestData);
							if (response.data.type) {
								this.authType[response.data.type] = true;
							}
							if (response.data.valid) {
								let userCookie = response.data.cookie;
								try {
									this.show.submitButtonAndCheckBoxConditions = true;
									if (!this.billingCode) {
										const response = await axios.post(`${import.meta.env.VITE_BACKEND_URL}order/find_accounts`, {
											cellphoneNumber: this.phoneValidate,
											cookie: userCookie,
											brand: brand.getBrand()
										})
										if (!response.data.cookieError) {
											this.serializeAccountSelect(response.data);
										} else {
											this.clearingFields();
										}
									} else {
										if (!this.authType[this.mesAuthThis]) {
											this.show.dataInput = true;
											this.show.billingCode = false;
											this.show.createContactMessage = true;
											this.show.maskedInformation = false;
											this.disabled.confirmButton = false;
										} else {
											const response = await axios.post(`${import.meta.env.VITE_BACKEND_URL}order/find_account_by_billing_code`, {
												cellphoneNumber: this.phoneValidate,
												billingCode: this.billingCode,
												cookie: userCookie
											})
											if (!response.data.cookieError) {
												this.serializeAccountSelect(response.data);
											} else {
												this.clearingFields();
											}
										}
									}
								} catch (error) {
									if (typeof error.response.data == "string") {
										this.orderResponse = error.response.data;
									} else if (error.response.data.length > 0) {
										this.orderResponse = error.response.data[0];
									} else if (Object.keys(error.response.data).length === 0) {
										this.orderResponse = "Server error. Please try another time...";
									} else {
										this.orderResponse = error.response.data;
									}
									this.show.throbberSecretCode = false;
									return;
								}
								if (this.secretCodeError) {
									this.secretCodeError = "";
								}
								this.show.secretCode = false;
								this.show.viaButtons = false;
								this.show.sendingInterval = false;
							} else {
								this.show.throbberSecretCode = false;
								if (response.data.tryCount) {
									this.secretCodeError = "Incorrect PIN. Please try again.";
									this.secretCode = "";
								}
								if (response.data.expired) {
									this.secretCodeError = "PIN has expired. Please try again.";
									this.secretCode = "";
								}
								console.log("TEST DATA VALIDATE PIN", response.data);
								if (!response.data.valid && !response.data.tryCount && !response.data.expired) {
									this.secretCodeError = "You have exceeded authorization attempts.";
									this.secretCode = "";
									this.disabled.secretCode = true;
									this.show.sendingInterval = false;
									clearInterval(this.sendInt);
									this.sendingIntervalCount = 60;
									this.disabled.TGButton = false;
									this.disabled.SMSButton = false;
									this.disabled.WhatsAppButton = false;
									this.sendCount = 0;
								}
								code = "";
							}
						} catch (error) {
							this.clearingFields();
						}

					}
				}
			}
		},
		customer: {
			handler(val) {
				this.$store.commit("setCustomer", val);
			},
			deep: true,
		},
		"customer.cellphone": {
			handler(val) {
				if (
					!this.customer.daytimeContactNumber.length ||
					val.indexOf(this.customer.daytimeContactNumber) >= 0
				) {
					this.customer.daytimeContactNumber = val;
				}
				//this.$refs.cellphoneInput.choose(this.phoneOptions.defaultCountry, true);
			},
			deep: true,
		},
	},
	computed: {
		titleIds() {
			return this.$store.getters.getTitleIds;
		},
		promoCodeId() {
			return this.$store.getters.getPromoCodeId;
		},
		installType() {
			return this.$store.getters.getInstallType;
		},
		banks() {
			return this.$store.getters.getBanks;
		},
		builtAddress() {
			return this.$store.getters.getBuiltAddress;
		},
		addresses() {
			return this.$store.getters.getAddressList;
		},
		unitNumber() {
			return this.$store.getters.getUnitNumber;
		},
		buildingName() {
			return this.$store.getters.getBuildingName;
		},
		fullAddress() {
			return this.$store.getters.getFullAddress;
		},
		zone() {
			return this.$store.getters.getZone;
		},
		provinces() {
			return this.$store.getters.getProvinces;
		},
		position() {
			return this.$store.getters.getPosition;
		},
		productDetails() {
			if (this.product && this.product !== '{}' && this.product.details.length) {
				return this.product.details.split(/\r?\n/g);
			}
			return null;
		},

		termsString() {
			const termsStrings = {
				"cybersmart": "I have read, understood and accept Cybersmart <a class=\"text-blue underline\" href=\"/assets/terms.pdf\" target=\"_blank\">Terms and Conditions</a>.",
				"ftta": "I have read, understood and accept FTTA <a class=\"text-blue underline\" href=\"/assets/terms_ftta.pdf\" target=\"_blank\">Terms and Conditions</a>.",
				"gigalight": "I have read, understood and accept Gigalight <a class=\"text-blue underline\" href=\"/assets/terms_gigalight.pdf\" target=\"_blank\">Terms and Conditions</a>."
			};
			return termsStrings[brand.getBrand()];
		}
	},
	methods: {
		serializeAccountSelect(array) {
			this.accountsList = [];
			this.userAccount = [];
			const unique = Array.from(new Set(array.map(item => JSON.stringify(item)))).map(item => JSON.parse(item));
			unique.map((data) => {
				let accountStatus = this.getAccountStatus(data.accountStatus);
				this.accountsList.push({
					value: data.accountId,
					label: `${data.customerName} (${accountStatus}) | ${data.accountId}`
				})
			});
			this.show.throbberSecretCode = false;
			this.show.accountsList = true;
			if (unique.length === 1) {
				this.userAccount = this.accountsList[0];
				// this.disabled.accountsList = true;
				this.disabled.confirmButton = false;
			}
		},
		accountListSelected() {
			console.log('changed')
			this.disabled.confirmButton = false;
		},
		async billingCodeValidate() {
			this.show.throbberBillingCode = true;
			try {
				const response = await axios.post(`${import.meta.env.VITE_BACKEND_URL}order/check_billing_code`, {
					billingCode: this.billingCode,
				})
				if (response.data.result) {
					this.getSecretCode(this.pinDeliveryChannel['SMS']);
					this.disabled.billingCode = true;
					this.show.checkBillingCode = false;
				} else {
					this.clearingFields();
				}
			} catch (error) {
				console.log(Object.keys(error.response.data).length);
				if (typeof error.response.data == "string") {
					this.orderResponse = error.response.data;
				} else if (error.response.data.length > 0) {
					this.orderResponse = error.response.data[0];
				} else if (Object.keys(error.response.data).length === 0) {
					this.orderResponse = "Server error. Please try another time...";
				} else {
					this.orderResponse = error.response.data;
				}
				this.show.throbberBillingCode = false;
			}
		},
		clearingFields() {
			if (!this.show.dataInput) {
				this.show.dataInput = true;
				this.show.billingCode = false;
				this.show.secretCode = false;
				this.disabled.cellphone = false;
				this.show.viaButtons = false;
				this.show.sendingInterval = false;
				this.show.accountsList = false;
				this.show.checkBillingCode = false;
				this.show.billingCodePinMessage = false;
				this.disabled.WhatsAppButton = false;
				this.disabled.TGButton = false;
				this.disabled.SMSButton = false;
				clearInterval(this.sendInt);
				this.sendingIntervalCount = 60;
				this.disabled.confirmButton = false;
				this.show.submitButtonAndCheckBoxConditions = false;
				this.show.maskedInformation = false;
			}
		},
		async cellphoneValidate(cellphone, obj) {
			this.phoneValidate = obj.number;
			if (this.show.dataInput) {
				this.show.dataInput = false;
			}
			if (this.show.viaButtons) {
				this.show.viaButtons = false;
			}
			if (this.show.billingCode) {
				this.show.billingCode = false;
			}
			if (obj.countryCode === "ZA") {
				if (obj.nationalNumber[0] !== "0") {
					this.phoneValidate = "0" + obj.nationalNumber;
				} else {
					this.phoneValidate = obj.nationalNumber;
				}
				obj.valid = this.phoneValidate.length === 10;
			}
			clearTimeout(this.debounce);
			if (obj.valid) {
				this.debounce = setTimeout(async () => {
					try {
						this.show.throbberCellPhone = true;
						this.disabled.cellphone = true;
						const response = await axios.post(`${import.meta.env.VITE_BACKEND_URL}order/cell_exist`, {
							cellphoneNumber: this.phoneValidate,
							brand: brand.getBrand()
						})
						if (response.data.result) {
							this.disabled.cellphone = false;
							if (this.show.dataInput) {
								this.show.dataInput = false;
								this.disabled.confirmButton = true;
							}
							this.show.viaButtons = true;
							this.show.throbberCellPhone = false;

						} else {
							this.disabled.cellphone = false;
							this.show.throbberCellPhone = false;
							if (this.installType === "upgrade") {
								this.show.billingCode = true;
							} else {
								this.clearingFields();
								console.log('A')
							}
						}
					} catch (error) {
						if (typeof error.response.data == "string") {
							this.orderResponse = error.response.data;
						} else if (error.response.data.length > 0) {
							this.orderResponse = error.response.data[0];
						} else if (Object.keys(error.response.data).length === 0) {
							this.orderResponse = "Server error. Please try another time...";
						} else {
							this.orderResponse = error.response.data;
						}
						this.show.throbberCellPhone = false;
					}
				}, 1001);
			}
		},
		async getSecretCode(pinDeliveryChannel) {
			this.secretCode = "";
			this.sendCount++;
			if (this.sendCount >= 3) {
				this.sendButtonsError = "Number of attempts exceeded. Re-send PIN not available";
			} else {
				this.secretCodeError = "";
				this.disabled.secretCode = false;
			}
			this.disabled.cellphone = true;
			this.disabled.WhatsAppButton = true;
			this.disabled.TGButton = true;
			this.disabled.SMSButton = true;

			if (this.secretCodeError) {
				this.secretCodeError = "";
			}
			let requestData = {
				phoneNumber: this.phoneValidate,
				pinDeliveryChannel: pinDeliveryChannel
			};
			if (this.billingCode) {
				requestData.billingCode = this.billingCode;
			} else {
				this.show.sendingInterval = true;
				this.show.secretCode = true;
			}
			try {
				const response = await axios.post(`${import.meta.env.VITE_AUTHSERVICE_URL}getSecretCode/`, requestData);
				if (response) {
					if (this.billingCode) {
						this.show.sendingInterval = true;
						this.show.secretCode = true;
						this.show.throbberBillingCode = false;
						this.primaryMaskedCellphone = response.data.primaryMaskedCellphone;
						this.primaryMaskedEmail = response.data.primaryMaskedEmail;
						this.show.maskedInformation = true;
					}
					this.sendInt = setInterval(() => {
						if (this.sendingIntervalCount > 0) {
							this.sendingIntervalCount = this.sendingIntervalCount - 1;
						} else {
							this.show.sendingInterval = false;
							if (this.sendCount <= 3) {
								this.disabled.WhatsAppButton = false;
								this.disabled.TGButton = false;
								this.disabled.SMSButton = false;
							}
							clearInterval(this.sendInt);
							this.sendingIntervalCount = 60;
						}
					}, 1000);
				}
			} catch (error) {
				this.clearingFields();
			}
		},
		printTitle() {
			console.log("title: ", JSON.stringify(this.customer.titleId));
			//this.customer.titleId = event.target.value;
		},
		loadRetry() { },
		async getUpdatedProductDetails() {
			if (this.zone &&
				this.zone.provider_name != "Cybersmart" &&
				this.installType == "existing"
			) {
				this.loading = true;

				try {
					const fD = new FormData();
					fD.set("promoCode", `${this.promoCodeId}-MIGRATION`);

					const response = await this.$http.post(
						`${import.meta.env.VITE_BACKEND_URL}feasibility/productdetails`,
						fD
					);
					console.log("product details response", response);

					if (response.data.products && response.data.products.length) {
						const tempProd = _.find(response.data.products, (p) => {
							return p.productid == this.product.productid;
						});
						if (tempProd) {
							this.product = tempProd;
							let productIds = {
								promocodeproductid: this.product.promocodeproductid,
								productid: this.product.productid,
								promocodeid: response.data.code
							};
							console.log("productIds old", this.productIds, ", new", productIds);
							this.productIds = productIds;
						}
					}
					this.loading = false;
				} catch (error) {
					console.log("product details error", error.response);
					this.loading = false;
				}
			}
		},
		makeUsername() {
			// Username = Street number + First 6 letters of the street address + unit number @ lightspeed.co.za
			// ( if this fails validation, append a - + today’s day at the end of the username, I.e <EMAIL>
			/* eslint-disable */
			const adParts = this.builtAddress.split(",");
			let strAdParts, strNum, strAd, unit;
			const atDomains = {
				"cybersmart": "@lightspeed.co.za",
				"ftta": "@fttafibre",
			};
			const atDomain = atDomains[brand.getBrand()] || "@lightspeed.co.za";

			if (adParts[0].indexOf("Unit") >= 0) {
				(strAdParts = adParts[1].split(" ")),
					(strNum = strAdParts.splice(0, 1)),
					(strAd = strAdParts.join("").substring(0, 6)),
					(unit = adParts[0].replace("Unit", "").trim());
			} else {
				(strAdParts = adParts[0].split(" ")),
					(strNum = strAdParts.splice(0, 1)),
					(strAd = strAdParts.join("").substring(0, 6)),
					(unit = "");
			}

			return `${strNum}${strAd}${unit}-${moment().format(
				"DD"
			)}${atDomain}`;
			/* eslint-enable */
		},
		areFieldsValid() {
			this.errors = [];

			if (this.show.dataInput) {
				if (!this.customer.titleId) {
					this.titleIdError = "Missing title.";
					this.errors.push("Missing title.");
				} else {
					this.titleIdError = "";
				}
				if (!this.customer.firstName) {
					this.firstNameError = "Missing first name.";
					this.errors.push("Missing first name.");
				} else if (!this.firstLastNameRegex.test(this.customer.firstName)) {
					this.firstNameError = "Letters, hyphen and apostrophe is allowed in first name.";
					this.errors.push("Letters, hyphen and apostrophe is allowed in first name.");
				} else {
					this.firstNameError = "";
				}
				if (!this.customer.lastName) {
					this.lastNameError = "Missing last name.";
					this.errors.push("Missing last name.");
				} else if (!this.firstLastNameRegex.test(this.customer.lastName)) {
					this.lastNameError = "Letters, hyphen and apostrophe is allowed in last name.";
					this.errors.push("Letters, hyphen and apostrophe is allowed in last name.");
				} else {
					this.lastNameError = "";
				}
				if (!this.customer.email) {
					this.emailError = "Missing email.";
					this.errors.push("Missing email.");
				} else if (!this.emailRegex.test(this.customer.email.trim())) {
					this.emailError = "Invalid email. (e.g. <EMAIL>)";
					this.errors.push("Invalid email. (e.g. <EMAIL>)");
				} else {
					this.emailError = "";
				}

				if (!this.authType[this.mesAuthAndAddMasterAccount] && !this.authType[this.mesAuthAndAddSubAccount]) {
					if (!this.customer.daytimeContactNumber.trim() && !this.show.dataInput) {
						this.daytimeContactNumberError = "Missing daytime contact number.";
						this.errors.push("Missing daytime contact number.");
					} else if (
						!this.numRegex.test(this.customer.daytimeContactNumber.trim()) ||
						this.customer.daytimeContactNumber.trim().length !== 10
					) {
						this.daytimeContactNumberError =
							"Invalid daytime contact number. (e.g. **********)";
						this.errors.push("Invalid daytime contact number. (e.g. **********)");
					} else {
						this.daytimeContactNumberError = "";
					}
					/*if (!this.customer.cellphone.trim()) {
					  this.cellphoneError = "Missing cellphone number.";
					  this.errors.push("Missing cellphone number.");
					} else if (
					  !this.numRegex.test(this.customer.cellphone.trim()) ||
					  this.customer.cellphone.trim().length !== 10
					) {
					  this.cellphoneError = "Invalid cellphone number. (e.g. **********)";
					  this.errors.push("Invalid cellphone number. (e.g. **********)");
					} else {
					  this.cellphoneError = "";
					}*/

					if (!this.customer.idNumber) {
						this.idNumberError = "Missing ID / Passport / Company Registration.";
						this.errors.push("Missing ID / Passport / Company Registration.");
					} else {
						const isPassport = this.passportRegex.test(this.customer.idNumber);
						if (!isPassport) {
							if (!this.compRegex.test(this.customer.idNumber)) {
								this.idNumberError =
									"Invalid ID / Passport / Company Registration. (e.g. 2001014800086 / 86YH34567 / 2001/014800/86)";
								this.errors.push(
									"Invalid ID / Passport / Company Registration. (e.g. 2001014800086 / 86YH34567 / 2001/014800/86)");
							} else {
								this.idNumberError = "";

								if (!this.customer.companyName) {
									this.companyNameError = "Missing company name.";
									this.errors.push("Missing company name.");
								} else {
									this.companyNameError = "";
								}
							}
						} else {
							this.idNumberError = "";
						}
					}
				}
				console.log(this.errors);
			}
			if (!this.tandcs) {
				this.tandcsError = "Please accept the terms and conditions.";
				this.errors.push("Please accept the terms and conditions.");
			} else {
				this.tandcsError = "";
			}

			return !this.errors.length;
		},
		async checkForm(e) {
			if (this.areFieldsValid()) {
				if (this.installType === "upgrade") {
					if (this.authType[this.mesAuthAndAddMasterAccount] || this.authType[this.mesAuthAndAddSubAccount]) {
						await this.createContact();
						await this.submitUpgradeDowngrade();
					} else {
						await this.submitUpgradeDowngrade();
					}
				} else {
					await this.submitOrder();
				}
			}

			e.preventDefault();
		},
		async createContact() {
			const userTitle = this.titleIds.find(item => item.value == this.customer.titleId);
			const contactType = this.authType[this.mesAuthAndAddMasterAccount] ? "General Contact" : "Sub Account Contact";
			this.loading = true;
			const tempBody = {
				masterAccountId: this.billingCode,
				title: userTitle.label,
				firstName: this.customer.firstName,
				lastName: this.customer.lastName,
				cellPhoneNumber: this.phoneValidate,
				workPhoneNumber: this.phoneValidate,
				emailAddress: this.customer.email.trim(),
				contactType: contactType,
				contactLevel: ""
			};

			try {
				const response = await axios.post(`${import.meta.env.VITE_BACKEND_URL}order/create_contact`, tempBody);

				if (response) {
					this.accountsList.push({
						value: this.billingCode,
						label: this.billingCode
					})
					this.userAccount = this.accountsList[0];
					console.log("ACCOUNT CREATED.......");
				}
			} catch (error) {
				this.loading = false;

				if (error.response) {
					if (typeof error.response.data == "string") {
						this.orderResponse = error.response.data;
					} else if (error.response.data.length > 0) {
						this.orderResponse = error.response.data[0];
					} else if (Object.keys(error.response.data).length === 0) {
						this.orderResponse = "Server error. Please try another time...";
					} else {
						this.orderResponse = error.response.data;
					}
				} else {
					this.orderResponse = "Server error. Please try again...";
				}
				return false;
			}
		},
		findSimilarAddress() {
			console.log("fullAddress: ", JSON.stringify(this.fullAddress));
			if (!this.addresses.length) {
				console.log("Empty zone addresses");
				return null;
			}

			let address = this.findAddressId(this.unitNumber, this.fullAddress.street_number,
				this.fullAddress.route, this.addresses);

			if (!address && this.fullAddress.routeShort) {
				address = this.findAddressId(this.unitNumber, this.fullAddress.street_number,
					this.fullAddress.routeShort, this.addresses);
			}

			return address;
		},
		findAddressId(unit, street_number, route, addressesToSearch) {
			if (!route) {
				return null;
			}
			const unitStr = unit ? "Unit " + unit + ", " : "";
			const addressPart = this.prepareAddressForCompare(unitStr + `${street_number || ""} ${route}`.trim());
			const addressesToSearchPrepared = addressesToSearch.map(address => {
				return {
					displayAddress: this.prepareAddressForCompare(address.displayAddress),
					id: address.id
				}
			});

			console.log("Looking for ", addressPart);
			console.log("In ", JSON.stringify(addressesToSearchPrepared));

			const customerAddress = addressesToSearchPrepared.find(address => address.displayAddress.includes(addressPart));

			return customerAddress ? customerAddress.id : null;
		},
		prepareAddressForCompare(address) {
			return address.toLowerCase().replace(/[,\s]/g, "");
		},
		async submitZone(userDetail) {
			// check for existing address / zone
			// _.find(this.addresses, (o) => {
			//   return o.displayAddress == this.builtAddress;
			// });

			const zone = {
				position: this.position,
				address: this.fullAddress,
				province: this.fullAddress.administrative_area_level_1,
				unit: this.unitNumber,
				provider_name: this.zone.provider_name,
				user: userDetail,
			};
			console.log("zone info", zone);

			try {
				const response = await this.$http.post(
					`${import.meta.env.VITE_BACKEND_URL}order/create_zone_record`,
					zone
				);
				console.log("zone response", response.data);

				if (response.data.status !== "Success") {
					console.log("zone create error", response.data);
					this.$store.commit('setOrderNumber', response.data.orderNumber)

					if (response.data.fakePass) {
						this.$router.push({
							name: "complete"
						});
					}

					return false;
				}

				this.addressCode = response.data.zoneCode;
				userDetail.addressCode = response.data.zoneCode;
			} catch (error) {
				this.loading = false;

				if (error.response) {
					if (typeof error.response.data == "string") {
						this.orderResponse = error.response.data;
					} else if (error.response.data.length > 0) {
						this.orderResponse = error.response.data[0];
					} else if (Object.keys(error.response.data).length === 0) {
						this.orderResponse = "Server error. Please try another time...";
					} else {
						this.orderResponse = error.response.data;
					}
				} else {
					this.orderResponse = "Server error. Please try again...";
				}
				return false;
			}

			return true;
		},
		async submitAddress(userDetail) {
			// add new address
			const address = {
				distribution_node_code: this.addressCode,
				address_unit: this.unitNumber || "",
				address_street: `${this.fullAddress.street_number || ""} ${this.fullAddress.route || this.fullAddress.street_name
					}`.trim(),
				orderDetails: userDetail
			};
			console.log("address info", address);

			try {
				const response = await this.$http.post(
					`${import.meta.env.VITE_BACKEND_URL}order/add_address`,
					address
				);
				console.log("address response", response);

				if (response.data.fakePass) {
					this.$store.commit('setOrderNumber', response.data.orderNumber)
					this.$router.push({
						name: "complete"
					});

					return false;
				}

				this.addressId = response.data;
			} catch (error) {
				this.loading = false;

				if (error.response) {
					if (typeof error.response.data == "string") {
						this.orderResponse = error.response.data;
					} else if (error.response.data.length > 0) {
						this.orderResponse = error.response.data[0];
					} else if (Object.keys(error.response.data).length === 0) {
						this.orderResponse = "Server error. Please try another time...";
					} else {
						this.orderResponse = error.response.data;
					}
				} else {
					this.orderResponse = "Server error. Please try again...";
				}

				return false;
			}

			return true;
		},
		getCustomerInfo() {
			return {
				username: this.makeUsername(),
				titleId: this.customer.titleId,
				firstName: this.customer.firstName,
				lastName: this.customer.lastName,
				email: this.customer.email.trim(),
				daytimeContactNumber: this.customer.daytimeContactNumber,
				cellphone: this.phoneValidate,
				idNumber: this.customer.idNumber,
				companyName: this.customer.companyName,
				vatNumber: this.customer.vatNumber,
				accountId: this.customer.accountId
			};
		},
		async changeCustomerInfoInBackend() {
			console.log("DATA IN FRONTEND NOW ======>", this.customer);
			if (!this.billingCode || (this.authType[this.mesAuthAndAddMasterAccount] || this.authType[this.mesAuthAndAddSubAccount])) {
				const response = await axios.post(`${import.meta.env.VITE_BACKEND_URL}order/lookup_existing_customer`, {
					cellphoneNumber: this.phoneValidate,
					accountId: this.userAccount
				});
				if (!response.data.result && response.data.result !== undefined || !response.data) {
					console.log("DATA FROM BACKEND ======>", response.data);
					return false;
				}
				console.log("DATA TO BACKEND NOT REBUILT ======>", response.data);
				for (let key in response.data) {
					if (key === "titleId") {
						const userTitle = this.titleIds.find(item => item.label === response.data[key]);
						console.log("USER TITLE", userTitle.value);
						if (userTitle) {
							this.customer[key] = userTitle.value
						} else {
							this.customer[key] = 11;
						}
					} else {
						this.customer[key] = response.data[key];
					}
				}
				this.customer['accountId'] = this.userAccount;
			} else {
				this.customer['accountId'] = this.userAccount;
				this.customer['cellphone'] = this.phoneValidate;
			}
			console.log("DATA TO BACKEND AFTER REBUILD ======>", this.customer);
			return this.customer;
		},
		getProductInfo() {
			return {
				productIds: this.productIds,
				product_details: {
					code: this.product.code,
					name: this.product.name,
					provider_name: this.zone.provider_name,
					monthly_price: `R${this.product.monthly}`,
					once_off_fees:
						this.product.setup == "0.00"
							? "FREE Activation"
							: `R${this.product.setup} INCL. VAT Once off setup fee`,
					install_type: this.installType,
				},
			};
		},
		getAddressInfo() {
			return {
				addressCode: this.addressCode,
				physical_city: this.fullAddress.administrative_area_level_2,
				physical_code: this.fullAddress.postal_code,
				physical_country: this.fullAddress.country,
				physical_line_1: `${this.fullAddress.street_number || ""} ${this.fullAddress.premise ||
					this.fullAddress.route ||
					this.fullAddress.street_name
					}`,
				physical_line_2: "",
				physical_suburb: this.fullAddress.political,
				physical_province: this.fullAddress.administrative_area_level_1,
				physical_unit_number: this.unitNumber,
				position: this.position,
				locality: this.fullAddress.locality,
			};
		},
		getUserInput() {
			const agentId = store.state.agentId;
			return {
				...this.getCustomerInfo(),
				...this.getAddressInfo(),
				...this.getProductInfo(),
				...agentId && { agentId },  // If agentId is defined add it to the user input
			};
		},
		async submitUpgradeDowngrade() {
			this.loading = true;
			console.log("productIds", this.productIds);
			console.log("product", this.product);

			let userInformation;
			try {
				userInformation = !this.show.dataInput || this.show.dataInput && (this.authType[this.mesAuthAndAddMasterAccount] || this.authType[this.mesAuthAndAddSubAccount]) ? await this.changeCustomerInfoInBackend() : false;

				if (!this.show.dataInput && !userInformation) {
					this.clearingFields();
					this.loading = false;
					this.orderResponse = "User data not found, please try again or fill in the data yourself.";
					return;
				}
			} catch (error) {
				this.loading = false;
				if (error.response) {
					if (typeof error.response.data == "string") {
						this.orderResponse = error.response.data;
					} else if (error.response.data.length > 0) {
						this.orderResponse = error.response.data[0];
					} else if (Object.keys(error.response.data).length === 0) {
						this.orderResponse = "Server error. Please try another time...";
					} else {
						this.orderResponse = error.response.data;
					}
				} else {
					this.orderResponse = "Server error. Please try again...";
				}
				return;
			}

			let userInput = this.getUserInput();

			console.log("upgrade_downgrade userInput", userInput);
			try {
				const upgradeDowngradeUrl = `${import.meta.env.VITE_BACKEND_URL}order/upgrade_downgrade`;
				let response = await this.$http.post(upgradeDowngradeUrl, userInput);
				console.log("upgrade_downgrade response", response);

				this.$router.push({
					name: "upgradeRequestSent",
				});
			} catch (e) {
				console.log("Upgrade downgrade error", e);
				this.orderResponse = "This upgrade cannot be processed, please email " +
					"<EMAIL> with your request";
			}
			this.loading = false;
		},
		async submitOrder() {
			this.loading = true;
			let userInformation;
			try {
				userInformation = !this.show.dataInput ? await this.changeCustomerInfoInBackend() : false;

				if (!this.show.dataInput && !userInformation) {
					this.clearingFields();
					this.loading = false;
					this.orderResponse = "User data not found, please try again or fill in the data yourself.";
					return;
				}
			} catch (error) {
				this.loading = false;
				if (error.response) {
					if (typeof error.response.data == "string") {
						this.orderResponse = error.response.data;
					} else if (error.response.data.length > 0) {
						this.orderResponse = error.response.data[0];
					} else if (Object.keys(error.response.data).length === 0) {
						this.orderResponse = "Server error. Please try another time...";
					} else {
						this.orderResponse = error.response.data;
					}
				} else {
					this.orderResponse = "Server error. Please try again...";
				}
				return;
			}

			this.orderResponse = null;
			let userInput = this.getUserInput();

			console.log("TEST DATA userInput", userInput);


			// handle address / zone stuff
			this.addressId = this.findSimilarAddress();
			console.log("found addressId: ", this.addressId);
			console.log("found zone", this.addressCode);

			if (!this.addressId && !this.addressCode || (this.zone?.zone_type && this.zone.zone_type === PROVIDER_FEASIBILITY)) {
				const res = await this.submitZone(userInput);

				if (!res) {
					return;
				}
			}

			if (!this.addressId) {
				const res = await this.submitAddress(userInput);

				if (!res) {
					return;
				}
			}

			if (typeof this.addressId === "object") {
				userInput.addressId = this.addressId.addressId || this.addressId.id;
			} else {
				userInput.addressId = this.addressId;
			}
			if (this.zone.preOrder) {
				userInput.preOrder = true;
			}
			console.log("LOOK HERE::", userInput);

			/* eslint-disable */
			this.$http
				// .post(`${import.meta.env.VITE_BACKEND_URL}order/testpdf`, userInput)
				.post(`${import.meta.env.VITE_BACKEND_URL}order/create_customer`, userInput)
				.then((response) => {
					console.log("TEST DATA CREATE_CUSTOMER RESPONSE", response.data);
					this.$store.commit('setOrderNumber', response.data.orderNumber)

					this.loading = false;

					const newUrl = response.data.fakePass ? "complete" : "inviteSent";
					this.$router.push({
						name: newUrl
					});
				})
				.catch((error) => {
					this.loading = false;
					if (error.response) {
						if (typeof error.response.data == "string") {
							this.orderResponse = error.response.data;
						} else if (error.response.data.length > 0) {
							this.orderResponse = error.response.data[0];
						} else if (Object.keys(error.response.data).length === 0) {
							this.orderResponse = "Server error. Please try another time...";
						} else {
							this.orderResponse = error.response.data;
						}
					} else {
						this.orderResponse = "Server error. Please try again...";
					}
				});
			/* eslint-enable */
		},
		getAccountStatus(status) {
			if (status === "Active Account") {
				return this.accountStatusesInSelect["Active Account"];
			} else if (status === "Suspended Account") {
				return this.accountStatusesInSelect["Suspended Account"];
			} else {
				return this.accountStatusesInSelect["Inactive Account"];
			}
		},
		get: _.get,
		formatProductFeatures(details) {
			const lines = details.split('\r\n');
			
			// Trim any leading or trailing whitespace from each line (optional)
			return lines.map(line => line.trim());
		},
	},
};
</script>

<style scoped>

:deep(.vti__dropdown),
:deep(.vti__input) {
	@apply !px-4 !py-2 !bg-white !rounded-xl !border !border-solid !border-slate-300 group-focus-within:border-blue relative
}

:deep(.vti__input:before) {
	content: 'Phone Number';
	@apply absolute left-0 top-0
}

:deep(.vti__flag) {
	@apply rounded-full
}

</style>