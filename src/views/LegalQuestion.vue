<template>
	<div class="faq columns">
		<div class="column is-8">
			<div class="faq-questions">
				<h2 class="title">{{ legal.name }}</h2>
				<div class="has-text-weight-normal" v-html="legal.html"></div>
				<router-link class="button is-primary faq-button" to="/legal" style="margin-top: 3rem">GO
					BACK</router-link>
			</div>
		</div>
		<div class="column is-4">
			<div class="faq-contact-us">
				<div class="box">
					<h2 class="title">Can't find the answer you're looking for?</h2>
					<router-link class="button is-primary faq-button" to="/contact-us">CONTACT US</router-link>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';

const store = useStore();
const route = useRoute();
const type = route.params.type;
const question = route.params.question.split('_').join(' ');

onMounted(async () => {
	if (!store.getters.getLegal) {
		await store.dispatch('fetchLegal');
	}
});

const legal = computed(() => {
	return store.getters.getLegal.find((v: any) => v.category === type && v.name === question);
});
</script>