<template>
	<div>
		<Loader :loading="loading" />
		<template v-if="page">
			<component
				v-bind="block"
				v-for="block in page.attributes.content"
				:key="block.id"
				:is="getComponent(block.__component)"
			/>
		</template>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, defineAsyncComponent, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { useHead } from '@unhead/vue'
import Loader from '@/components/Loader.vue'

interface Props {
	slug: string[]
}

const props = defineProps<Props>()

const route = useRoute()
const page = ref<any>(null)
const router = useRouter()
const store = useStore()

const loading = ref<boolean>(true)

const fetchPage = async () => {
	loading.value = true
	page.value = null
	try {
		const slug = (route.params.slug as string[]).join('/')
		const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}pages?populate=deep&filters[slug][$eq]=${slug}`)
		const data = await response.json()
		if (data.data.length > 0) {
			page.value = data.data[0]
			// Set header colour
			store.commit('setDarkNavigation', page.value.attributes.darkNavigation)
		} else {
			router.push('/404')
		}
	} catch (error: any) {
		console.error('An error occurred:', error)
	} finally {
		loading.value = false
	}
}

// Reactive SEO meta
const metaTitle = computed(() => {
	return page.value ? `Cybersmart | ${page.value.attributes.seo.metaTitle}` : 'Cybersmart | Page not found'
})

const metaDescription = computed(() => {
	return page.value ? page.value.attributes.seo.metaDescription : 'Page not found'
})

// Reactive head setup using computed getter
useHead({
	title: metaTitle,
	meta: [
		{
			name: 'description',
			content: metaDescription,
		},
	],
})

const getComponent = (component: string) => {
	const parts = component.split('.')
	const componentName = parts[1].split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('')
	return defineAsyncComponent(() => import(`@/blocks/${componentName}.vue`))
}

// Ensure page is updated when slug from url changes
watch(
	() => props.slug,
	fetchPage,
	{ deep: true }
)

onMounted(fetchPage)
</script>
