<template>
	<div class="container my-20 md:my-24">
		<h1 class="text-h1 text-digital-black font-semibold mb-6">THANK YOU FOR YOUR FIBRE ORDER</h1>
		<p v-if="orderNumber" class="text-slate-500 font-light mb-6">Your order number is&nbsp;
			<span class="font-semibold">{{ orderNumber }}</span>
		</p>
		<p class="text-slate-500 font-light mb-4">You will be contacted within 1 business day via WhatsApp with a
			provisional schedule.</p>
		<p class="text-slate-500 font-light mb-4">We use a third party provider igenface.com facial recognition
			technology to capture your debit order details and keep them safe by matching your bank details with your
			face.</p>
		<p class="text-slate-500 font-light mb-6">You will be sent an sms to the phone number given during the order,
			please take a selfie of your face when asked, you will then be sent an sms requesting you to fill in your
			bank details.</p>
		<LeaveReview />
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useStore } from 'vuex';
import LeaveReview from "@/components/LeaveReview.vue";

const store = useStore();
const orderNumber = computed(() => store.state.orderNumber);
</script>