<template>
	<div class="container py-20 md:py-24">
		<SectionIntro
			subtitle="Cybersmart"
			title="Network Notices"
		/>
		<div v-if="filteredNotices.length" class="flex flex-col gap-8">
			<div v-for="(notice, index) in filteredNotices" :key="index" class="flex flex-col gap-4">
				<div class="flex gap-4">
					<DsBadge v-bind="{type:'accent',size:'small'}">{{ formatDate(notice.revealDate) }}</DsBadge>
					<div class="text-h3">{{ notice.title }}</div>
				</div>
				<p class="text-slate-500 font-light ">{{ notice.description }}</p>
			</div>
		</div>
		<div v-else class="text-h3">There are currently no network notifications</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';
import SectionIntro from '@/blocks/SectionIntro.vue';

interface Notice {
	title: string;
	description: string;
	revealDate: string;
	endDate: string;
}

const notices = ref<Notice[]>([]);

const filteredNotices = computed(() => {
	const now = new Date();
	return notices.value.filter(notice => {
		const endDate = new Date(notice.endDate);
		const revealDate = new Date(notice.revealDate);
		return endDate > now && revealDate < now;
	});
});

const formatDate = (date: string) => {
	const options: Intl.DateTimeFormatOptions = {
		hour: '2-digit',
		minute: '2-digit',
		day: '2-digit',
		month: 'long',
		year: 'numeric'
	};
	return new Date(date).toLocaleDateString(undefined, options);
};

onMounted(async () => {
	try {
		const response = await axios.get<Notice[]>(`${import.meta.env.VITE_BACKEND_URL}notices`);
		console.log("Get notices: ", response.data);
		notices.value = response.data;
	} catch (error) {
		console.error("Error fetching notices:", error);
	}
});
</script>