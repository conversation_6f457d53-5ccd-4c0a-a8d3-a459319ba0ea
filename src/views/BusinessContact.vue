<template>
	<div>
		<div v-if="noProducts === 'true'" class="fixed top-8 inset-x-0 w-fit m-auto z-50">
			<DsToast class="" title="Contact Us" variant="primary">
				Please contact us below - Cybersmart would love to hear your requirements at this address. Please fill
				out the form below and one of our Sales Team will be in touch as soon as possible.
			</DsToast>
		</div>
		<FeasibilitySearch subtitle="Find your fibre" title="Search available business connectivity"
			description="Enter your address below to find the best business fibre options available to you."
			business-only="true" />
		<div ref="contactFormRef" class="container mt-20 md:mt-24">
			<div class="grid grid-cols-1 gap-7 lg:grid-cols-2 lg:gap-14 mb-20 md:mb-24">
				<div class="flex flex-col justify-between gap-10">
					<div>
						<div class="text-subtitle text-blue font-semibold mb-8 uppercase">business Connectivity</div>
						<div class="text-h1 text-digital-black font-semibold mb-12">Speak with our team</div>
						<p class="text-slate-500 font-light mb-8">At Cybersmart, we know the complexities that come with
							running a business and the dedicated connectivity that is required to operate in the modern
							world - which is why we have a business fibre-dedicated team ready to help with your every
							need.
						</p>
						<p class="text-slate-500 font-light">Connect your business with confidence today, with
							Cybersmart.
						</p>
					</div>
				</div>
				<div class="bg-slate-50 rounded-3xl px-4 md:px-7 py-12 flex flex-col justify-center">
					<form @submit.prevent="validateAndSubmit" class="flex flex-col gap-5">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-5">
							<DsTextInput label="Name" name="name" v-model="v$.name.$model"
								:invalid="v$.name.$dirty && v$.name.$invalid"
								:valid="v$.name.$dirty && !v$.name.$invalid" invalid-message="Please enter your name" />
							<DsTextInput label="Email" name="email" v-model="v$.email.$model"
								:invalid="v$.email.$dirty && v$.email.$invalid"
								:valid="v$.email.$dirty && !v$.email.$invalid"
								invalid-message="Please enter a valid email" />
						</div>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-5">
							<DsTextInput label="Phone" name="phone" v-model="v$.phone.$model"
								:invalid="v$.phone.$dirty && v$.phone.$invalid"
								:valid="v$.phone.$dirty && !v$.phone.$invalid"
								invalid-message="Please enter your phone number" />
							<DsTextInput label="Company Name" name="companyName" v-model="v$.companyName.$model"
								:invalid="v$.companyName.$dirty && v$.companyName.$invalid"
								:valid="v$.companyName.$dirty && !v$.companyName.$invalid"
								invalid-message="Please enter your company name" />
						</div>

						<DsSelectInput label="Company Size (Number of users)" placeholder="Please Select"
							name="companySize" :options="companySizeOptions" v-model="v$.companySize.$model"
							:invalid="v$.companySize.$dirty && v$.companySize.$invalid"
							:valid="v$.companySize.$dirty && !v$.companySize.$invalid"
							invalid-message="Please select your company size" />
						<DsTextArea label="Message" name="message" v-model="v$.message.$model"
							:invalid="v$.message.$dirty && v$.message.$invalid"
							:valid="v$.message.$dirty && !v$.message.$invalid"
							invalid-message="Please enter your message" />
						<div>
							<DsButton button-type="submit" :label="isSubmitting ? 'Sending...' : 'Send Enquiry'"
								type="primary" :disabled="isSubmitting || v$.invalid" />
						</div>
						<div v-if="error" class="fixed top-8 inset-x-0 w-fit m-auto z-50">
							<DsToast class="" title="Error" variant="danger">
								{{ error }}
							</DsToast>
						</div>
						<div v-if="success" class="fixed top-8 inset-x-0 w-fit m-auto z-50">
							<DsToast class="" title="Thank you." variant="success">
								Your message has been sent. We'll be in touch shortly
							</DsToast>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, onMounted, nextTick } from 'vue';
import { useVuelidate } from '@vuelidate/core'
import { required, email } from '@vuelidate/validators'
import { useSubmitForm } from '@/composables/submitForm';
import FeasibilitySearch from '@/blocks/FeasibilitySearch.vue';
import { useBrevoApi } from '@/composables/useBrevoApi';

interface Props {
	noProducts?: null | string
}

const props = withDefaults(defineProps<Props>(), {
	noProducts: null
})

interface FormData {
	name: string;
	email: string;
	phone: string;
	companySize: string;
	companyName: string;
	message: string;
	scheduleDemo: boolean;
}

const formData = reactive<FormData>({
	name: '',
	email: '',
	phone: '',
	companySize: '',
	companyName: '',
	message: '',
	scheduleDemo: false,
});

const rules = {
	name: { required },
	email: { required, email },
	phone: { required },
	companySize: { required },
	companyName: { required },
	message: { required },
}

// Initialize Vuelidate
const v$ = useVuelidate(rules, formData);

const companySizeOptions = [
	{ label: '1-10', value: '1-10' },
	{ label: '11-50', value: '11-50' },
	{ label: '51-100', value: '51-100' },
	{ label: '100+', value: '100+' },
];

const contactFormRef = ref<HTMLElement | null>(null);

// Initialize the composable and extract its properties and methods
const { isSubmitting, error, success, submitForm } = useSubmitForm('Business Contact', formData);

const resetForm = () => {
	formData.name = ''
	formData.email = ''
	formData.phone = ''
	formData.companySize = ''
	formData.companyName = ''
	formData.message = ''
	formData.scheduleDemo = false
	v$.value.$reset()
};

const { createContact } = useBrevoApi();

const validateAndSubmit = async () => {
	try {
		// If validation passes, submit the form
		if (!v$.value.$invalid) {
			// First, try to create the contact in Brevo
			await createContact(formData);

			// If Brevo creation succeeds, submit the form normally
			await submitForm();

			// If submission is successful, reset the form
			if (success.value) {
				resetForm();
			}
		}
	} catch (err: unknown) {
		console.error('Form submission error:', err);
		// Type guard to check if err is an Error object
		if (err instanceof Error) {
			error.value = err.message;
		} else {
			error.value = 'Failed to submit form. Please try again.';
		}
	}
};

const scrollToContactForm = () => {
	nextTick(() => {		
		setTimeout(() => {
			if (contactFormRef.value) {
				contactFormRef.value.scrollIntoView({ behavior: 'smooth' });
			}
		}, 500);
	});
};

watch(() => props.noProducts, (newValue) => {
	if (newValue === 'true') {
		scrollToContactForm();
	}
}, { immediate: true });

onMounted(() => {
	if (props.noProducts === 'true') {
		scrollToContactForm();
	}
});
</script>