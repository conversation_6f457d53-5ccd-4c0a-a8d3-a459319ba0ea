// Interface automatically generated by schemas-to-ts

import { BreadcrumbLink } from '../../components/interfaces/BreadcrumbLink';
import { Media } from '../../../common/schemas-to-ts/Media';
import { BreadcrumbLink_Plain } from '../../components/interfaces/BreadcrumbLink';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { BreadcrumbLink_NoRelations } from '../../components/interfaces/BreadcrumbLink';

export interface Hero {
  breadCrumbTrail: BreadcrumbLink[];
  title: string;
  description?: string;
  image: { data: Media };
}
export interface Hero_Plain {
  breadCrumbTrail: BreadcrumbLink_Plain[];
  title: string;
  description?: string;
  image: Media_Plain;
}

export interface Hero_NoRelations {
  breadCrumbTrail: BreadcrumbLink_NoRelations[];
  title: string;
  description?: string;
  image: number;
}

