// Interface automatically generated by schemas-to-ts

import { Button } from '../../components/interfaces/Button';
import { Button_Plain } from '../../components/interfaces/Button';
import { Button_NoRelations } from '../../components/interfaces/Button';

export interface CtaBanner {
  inlineStyle?: boolean;
  title: string;
  description?: string;
  button: Button;
}
export interface CtaBanner_Plain {
  inlineStyle?: boolean;
  title: string;
  description?: string;
  button: Button_Plain;
}

export interface CtaBanner_NoRelations {
  inlineStyle?: boolean;
  title: string;
  description?: string;
  button: Button_NoRelations;
}

