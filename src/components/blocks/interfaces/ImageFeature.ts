// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Button } from '../../components/interfaces/Button';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { Button_Plain } from '../../components/interfaces/Button';
import { Button_NoRelations } from '../../components/interfaces/Button';

export enum ImageAlign {
  Left = 'left',
  Right = 'right',}

export interface ImageFeature {
  image?: { data: Media };
  imageAlign?: ImageAlign;
  content?: any;
  icon?: string;
  button: Button[];
}
export interface ImageFeature_Plain {
  image?: Media_Plain;
  imageAlign?: ImageAlign;
  content?: any;
  icon?: string;
  button: Button_Plain[];
}

export interface ImageFeature_NoRelations {
  image?: number;
  imageAlign?: ImageAlign;
  content?: any;
  icon?: string;
  button: Button_NoRelations[];
}

