// Interface automatically generated by schemas-to-ts

import { LinkCard } from '../../components/interfaces/LinkCard';
import { LinkCard_Plain } from '../../components/interfaces/LinkCard';
import { LinkCard_NoRelations } from '../../components/interfaces/LinkCard';

export interface LinkCardGrid {
  columns: number;
  linkCards: LinkCard[];
}
export interface LinkCardGrid_Plain {
  columns: number;
  linkCards: LinkCard_Plain[];
}

export interface LinkCardGrid_NoRelations {
  columns: number;
  linkCards: LinkCard_NoRelations[];
}

