// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface LogoBanner {
  title?: string;
  logos: { data: Media[] };
}
export interface LogoBanner_Plain {
  title?: string;
  logos: Media_Plain[];
}

export interface LogoBanner_NoRelations {
  title?: string;
  logos: number[];
}

