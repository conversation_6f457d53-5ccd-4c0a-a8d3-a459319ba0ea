// Interface automatically generated by schemas-to-ts

import { FeatureCard } from '../../components/interfaces/FeatureCard';
import { FeatureCard_Plain } from '../../components/interfaces/FeatureCard';
import { FeatureCard_NoRelations } from '../../components/interfaces/FeatureCard';

export interface FeatureCardGrid {
  featureCards: FeatureCard[];
  columns: number;
}
export interface FeatureCardGrid_Plain {
  featureCards: FeatureCard_Plain[];
  columns: number;
}

export interface FeatureCardGrid_NoRelations {
  featureCards: FeatureCard_NoRelations[];
  columns: number;
}

