// Interface automatically generated by schemas-to-ts

import { Testimonial } from '../../../api/testimonial/content-types/testimonial/testimonial';
import { Testimonial_Plain } from '../../../api/testimonial/content-types/testimonial/testimonial';

export interface TestimonialSlider {
  subtitle?: string;
  title?: string;
  testimonials: { data: Testimonial[] };
}
export interface TestimonialSlider_Plain {
  subtitle?: string;
  title?: string;
  testimonials: Testimonial_Plain[];
}

export interface TestimonialSlider_NoRelations {
  subtitle?: string;
  title?: string;
  testimonials: number[];
}

