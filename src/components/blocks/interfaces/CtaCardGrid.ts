// Interface automatically generated by schemas-to-ts

import { CtaCard } from '../../components/interfaces/CtaCard';
import { CtaCard_Plain } from '../../components/interfaces/CtaCard';
import { CtaCard_NoRelations } from '../../components/interfaces/CtaCard';

export interface CtaCardGrid {
  columns: number;
  cards: CtaCard[];
}
export interface CtaCardGrid_Plain {
  columns: number;
  cards: CtaCard_Plain[];
}

export interface CtaCardGrid_NoRelations {
  columns: number;
  cards: CtaCard_NoRelations[];
}

