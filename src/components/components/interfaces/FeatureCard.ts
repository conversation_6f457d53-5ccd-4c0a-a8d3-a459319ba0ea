// Interface automatically generated by schemas-to-ts

export interface FeatureCard {
  title?: string;
  description?: string;
  icon?: string;
  linkText?: string;
  linkUrl?: string;
}
export interface FeatureCard_Plain {
  title?: string;
  description?: string;
  icon?: string;
  linkText?: string;
  linkUrl?: string;
}

export interface FeatureCard_NoRelations {
  title?: string;
  description?: string;
  icon?: string;
  linkText?: string;
  linkUrl?: string;
}

