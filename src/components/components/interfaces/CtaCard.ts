// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface CtaCard {
  title: string;
  description?: string;
  tags?: any;
  image?: { data: Media };
  url: string;
}
export interface CtaCard_Plain {
  title: string;
  description?: string;
  tags?: any;
  image?: Media_Plain;
  url: string;
}

export interface CtaCard_NoRelations {
  title: string;
  description?: string;
  tags?: any;
  image?: number;
  url: string;
}

