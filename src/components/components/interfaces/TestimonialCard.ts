// Interface automatically generated by schemas-to-ts

export interface TestimonialCard {
  author: string;
  testimonial: string;
  location?: string;
  rating?: number;
}
export interface TestimonialCard_Plain {
  author: string;
  testimonial: string;
  location?: string;
  rating?: number;
}

export interface TestimonialCard_NoRelations {
  author: string;
  testimonial: string;
  location?: string;
  rating?: number;
}

