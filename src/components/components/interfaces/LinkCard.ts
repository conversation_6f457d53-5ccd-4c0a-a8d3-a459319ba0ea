// Interface automatically generated by schemas-to-ts

export enum UrlTarget {
  Self = '_self',
  Blank = '_blank',
  Parent = '_parent',
  Top = '_top',}

export interface LinkCard {
  title?: string;
  descripion?: string;
  url: string;
  urlTarget?: UrlTarget;
  featureList?: any;
}
export interface LinkCard_Plain {
  title?: string;
  descripion?: string;
  url: string;
  urlTarget?: UrlTarget;
  featureList?: any;
}

export interface LinkCard_NoRelations {
  title?: string;
  descripion?: string;
  url: string;
  urlTarget?: UrlTarget;
  featureList?: any;
}

