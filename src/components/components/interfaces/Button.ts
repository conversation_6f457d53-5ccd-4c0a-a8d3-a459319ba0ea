// Interface automatically generated by schemas-to-ts

export enum Type {
  Primary = 'primary',
  Secondary = 'secondary',
  Outline = 'outline',
  Ghost = 'ghost',}

export interface Button {
  label?: string;
  url?: string;
  type?: Type;
  iconBefore?: string;
  iconAfter?: string;
}
export interface Button_Plain {
  label?: string;
  url?: string;
  type?: Type;
  iconBefore?: string;
  iconAfter?: string;
}

export interface Button_NoRelations {
  label?: string;
  url?: string;
  type?: Type;
  iconBefore?: string;
  iconAfter?: string;
}

