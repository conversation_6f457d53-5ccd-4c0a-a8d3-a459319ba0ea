// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Accordion } from './Accordion';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { Accordion_Plain } from './Accordion';
import { Accordion_NoRelations } from './Accordion';

export enum ImageAlign {
  Left = 'left',
  Right = 'right',}

export interface FaQs {
  imageAlign?: ImageAlign;
  image?: { data: Media };
  faqs: Accordion[];
  title?: string;
  description?: string;
}
export interface FaQs_Plain {
  imageAlign?: ImageAlign;
  image?: Media_Plain;
  faqs: Accordion_Plain[];
  title?: string;
  description?: string;
}

export interface FaQs_NoRelations {
  imageAlign?: ImageAlign;
  image?: number;
  faqs: Accordion_NoRelations[];
  title?: string;
  description?: string;
}

