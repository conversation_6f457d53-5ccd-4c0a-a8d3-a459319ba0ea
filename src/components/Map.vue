<template>
	<DsModal :open="geoCodeLoaded" :block-close="true">
		<div class="flex max-lg:flex-col justify-between items-center gap-10">
			<div class="flex flex-col gap-12">
				<div class="text-stone-800 text-h1 font-semibold">Confirm your address</div>
				<p class="text-p text-slate-500 font-light">{{ this.address }}</p>
				<div class="flex gap-5">
					<DsButton @click="confirmAddress()" label="Confirm" type="primary" />
					<DsButton class="border-solid" href="/feasibility-search" label="Change Address" type="outline" />
				</div>
			</div>
			<div class="lg:flex-[0_0_40%] w-full lg:min-w-[465px] h-[465px]">
				<GMapMap 
					:center="mapCoords" 
					:zoom="zoom" 
					map-type-id="roadmap"
					class="w-full h-full rounded-[20px] overflow-hidden" 
					:options="mapOptions"
				>
					<GMapMarker 
						:position="markerPosition" 
						:draggable="true" 
						:clickable="true"
						@dragend="updateCoords" 
					/>
				</GMapMap>
			</div>
		</div>
	</DsModal>
</template>

<script>
// import { GMapMap, GMapMarker } from 'vue-google-maps-community-fork'

export default {
	props: ["address"],
	data() {
		return {
			zoom: 18,
			markerPosition: {
				lat: 0,
				lng: 0,
			},
			mapCoords: {
				lat: 0,
				lng: 0,
			},
			mapOptions: {
				zoomControl: false,
				mapTypeControl: true,
				scaleControl: false,
				streetViewControl: false,
				rotateControl: false,
				fullscreenControl: false,
				disableDefaultUi: false
			},
			geoCodeLoaded: false
		}
	},
	methods: {
		updateCoords(location) {
			this.markerPosition = {
				lat: location.latLng.lat(),
				lng: location.latLng.lng()
			};
		},
		async confirmAddress() {
			this.$store.commit("setCoordsFromMap", this.markerPosition);
			this.$parent.showGeoModal = false;
			if (JSON.stringify(this.mapCoords) !== JSON.stringify(this.markerPosition)) {
				await this.$parent.getProducts();
			}
		},
	},
	async mounted() {
		try {
			// Wait for Google Maps to be loaded
			await this.$gmapApiPromiseLazy();

			// Create a new Geocoder instance
			const geocoder = new google.maps.Geocoder();

			const response = await new Promise((resolve, reject) => {
				geocoder.geocode({ address: this.address }, (results, status) => {
					if (status === 'OK') {
						resolve(results);
					} else {
						reject(new Error('Geocoding failed: ' + status));
					}
				});
			});

			this.mapCoords = {
				lat: response[0].geometry.location.lat(),
				lng: response[0].geometry.location.lng()
			};

			this.markerPosition = this.mapCoords;
			this.$store.commit("setCoordsFromMap", this.markerPosition);
			this.geoCodeLoaded = true;
		} catch (error) {
			console.error("Geocoding failed:", error);
			document.querySelector('.product').classList.remove('blur');
			document.querySelector('.cybersmart-footer').classList.remove('blur');
			document.querySelector('.smart-bar').classList.remove('blur');
			this.$parent.showGeoModal = false;
		}
	},
};
</script>
