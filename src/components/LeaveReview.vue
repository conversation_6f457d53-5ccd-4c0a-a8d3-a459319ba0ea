<template>
	<p v-if="reviewText !== ''" class="text-slate-500 font-light mb-6">
		{{ reviewText }}
	</p>
	<DsButton :label="linkText" type="primary" :href="reviewLink" target="_blank" />
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import brand from '@/brand';

// Computed properties
const reviewText = computed(() => {
	const text = {
		cybersmart: '',
		ftta: 'FTTA | Fibre To The Apartment would love your feedback. Post a review to our ',
		gigalight: '',
	};
	return text[brand.getBrand()];
});

const reviewLink = computed(() => {
	const links = {
		cybersmart: 'https://g.page/r/CY7n-qih1Cw-EBM/review',
		ftta: 'https://g.page/r/CQXAkG1cf52WEAg/review',
		gigalight: '',
	};
	return links[brand.getBrand()];
});

const linkText = computed(() => {
	const links = {
		cybersmart: 'Please leave a review',
		ftta: 'profile',
		gigalight: '',
	};
	return links[brand.getBrand()];
});
</script>