<template>
	<div :class="['p-5 relative', navClasses]">
		<div class="flex justify-between items-center">
			<DsLogo />
			<a href="#" v-if="mobileMenuActive" @click.prevent.stop="toggleMobileMenu" class="material-symbols-outlined text-sky-500">close</a>
			<a href="#" v-else @click.prevent.stop="toggleMobileMenu" class="material-symbols-outlined text-stone-800">menu</a>
		</div>
		
		<nav v-if="mobileMenuActive" class="pt-10 absolute left-0 z-20 bg-white w-full">
			<div class="container rounded-b-2xl shadow-lg">
				<div class="flex justify-center flex-wrap items-center gap-5 py-5">
					<router-link  to="/notices">
						<DsButton href="/notices" label="Network Status" type="outline" />
					</router-link>
					<DsButton href="https://my.cybersmart.co.za/" label="Client Portal" icon-before="account_circle" type="secondary" />
				</div>
				<div class="p-8 bg-slate-50 mb-8">
					<div class="text-sky-500 text-sm font-medium mb-8">Find Your Fibre</div>
					<router-link to="/feasibility-search">
						<div class="text-stone-800 text-base font-semibold capitalize flex items-center gap-2 mb-3">
							Check My Area Coverage
							<span class="material-symbols-outlined text-xl">arrow_right_alt</span>
						</div>
					</router-link>
					<div class="text-slate-500 text-sm font-light">Your address will inform us of the options available and get us started on your journey with Cybersmart.</div>
				</div>
				<div class="border-t border-neutral-200 pb-10">
					<div
						v-for="(parentNavItem, index) in navigation"
						:key="index"
						class="flex flex-col gap-2 border-b border-neutral-200"
					>
						<a
							@click.prevent.stop="setActiveSubmenu(parentNavItem)"
							class="text-stone-800 text-base font-semibold capitalize flex items-center justify-between py-6"
							href="#"
						>
							{{ parentNavItem.attributes.title }}
							<span
								v-if="parentNavItem.attributes.children.data.length > 0"
								:class="activeSubmenu === parentNavItem.id ? 'rotate-180' : ''"
								class="material-symbols-outlined text-base transition-all shrink-0"
							>keyboard_arrow_down</span>
						</a>
						<div v-if="parentNavItem.attributes.children.data.length > 0 && activeSubmenu === parentNavItem.id">
							<div v-for="(subNavSection, index) in parentNavItem.attributes.children.data">
								<div class="text-sky-500 text-sm font-medium">{{ subNavSection.attributes.title }}</div>
								<div v-if="subNavSection.attributes.children.data.length > 0" class="flex flex-col gap-6 my-6">
									<AppLink
										:to="subNavItem.attributes.url"
										v-for="subNavItem in subNavSection.attributes.children.data" 
										:key="subNavItem.id"
									>
										<div class="text-stone-800 text-base font-semibold capitalize flex items-center gap-2 hover:text-blue">
											<span v-if="subNavItem.attributes.icon" class="material-symbols-outlined text-sky-500 text-xl">{{ subNavItem.attributes.icon }}</span>
											{{ subNavItem.attributes.title }}
										</div>
									</AppLink>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="flex flex-col gap-5 py-5">
					<router-link
						to="/contact-us"
						class="text-stone-800 text-base font-semibold capitalize text-center"
					>
						Contact Us
					</router-link>
				</div>
			</div>
		</nav>
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import AppLink from './AppLink.vue'

interface Props {
	navigation: Navigation[]
}

defineProps<Props>()

const store = useStore()

const mobileMenuActive = computed(() => store.state.mobileMenuActive)

const activeSubmenu = computed(() => store.state.activeSubmenu)

const setActiveSubmenu = (navItem: NavigationItem) => {
	const newActiveSubmenu = navItem.attributes.children.data.length ? navItem.id : null;
	if (newActiveSubmenu !== store.state.activeSubmenu) {
		store.commit('setActiveSubmenu', newActiveSubmenu);
	}
	if (navItem.attributes.url) {
		navItem.attributes.url.startsWith('http') ? window.open(navItem.attributes.url, '_blank') : router.push(navItem.attributes.url);
	}
}

const toggleMobileMenu = () => {
	store.commit('toggleMobileMenuActive')
}

const navClasses = computed(() => {
	if (mobileMenuActive.value) {
		return `bg-white shadow-lg rounded-t-3xl`
	}
	return `bg-slate-50 rounded-full`
})

// const navClasses = computed(() => {
// 	var openNavBg, closedNavBg
// 	//Set the navigation background colour based on dark mode for navigation
// 	if (store.state.darkNavigation || store.state.darkMenu) {
// 		openNavBg = 'bg-white rounded-b-'
// 		closedNavBg = 'bg-white'
// 	}
// 	else {
// 		openNavBg = 'bg-white'
// 		closedNavBg = 'bg-slate-50'
// 	}
// 	if (activeSubmenu.value) {
// 		return `${openNavBg} rounded-3xl shadow-lg`
// 	}
// 	return `${closedNavBg} rounded-full`
// })

</script>