<template>
	<div class="bg-white fixed z-50 inset-0 flex flex-col justify-center" v-if="loading">
		<div class="mx-auto">
			<img v-if="showLogo" class="load-icon animated bounce" alt="Cybersmart Loading" src="/img/CYBERSMART-icon.svg">
			<p class="text-slate-500 font-light mt-4" v-html="message ? message : 'Loading...'"></p>
			<DsButton
				v-if="retry"
				label="Retry"
				type="secondary"
				@click="$emit('load-retry')"
				class="mt-4"
			/>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import brand from "@/brand"

interface Props {
	loading: boolean,
	message?: string | null,
	retry?: boolean
}

withDefaults(defineProps<Props>(), {
	loading: false,
	retry: false,
})

const showLogo = computed(() => {
	const show = {
		"cybersmart": true,
		"ftta": false,
		"gigalight": false,
	}
	return show[brand.getBrand()]
})
</script>

<style scoped>
.animated {
	animation-duration: 0.8s;
    animation-fill-mode: both;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

@-webkit-keyframes bounce {
	0%, 100% {
		-webkit-transform: translateY(0);
	}
	50% {
		-webkit-transform: translateY(-10px);
	}
}

@keyframes bounce {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-10px);
	}
}

.bounce {
	animation-name: bounce;
}
</style>
