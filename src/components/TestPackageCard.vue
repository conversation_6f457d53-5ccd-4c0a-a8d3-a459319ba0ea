<template>
	<div class="c-package-card px-8 py-14 rounded-3xl border relative overflow-hidden h-full" :class="cardClasses">
		<!-- Debug info -->
		<div class="absolute top-0 right-0 bg-red-500 text-white text-xs p-2 z-50 max-w-[200px]">
			<div>LOCAL TEST CARD</div>
			<div>active: {{ active }}</div>
			<div>cardClasses: {{ cardClasses }}</div>
			<div>title: {{ title?.substring(0, 20) }}...</div>
		</div>

		<div v-if="ribbonText" class="absolute top-0 left-0 w-full bg-blue py-2 flex items-center justify-center gap-2 rounted-t-3xl">
			<div v-if="ribbonIcon" class="material-symbols-outlined text-white">{{ ribbonIcon }}</div>
			<div class="text-white text-sm font-light">{{ ribbonText }}</div>
		</div>
		<div class="flex flex-col justify-between items-center text-center h-full">
			<div class="text-2xl lg:text-2xl 2xl:text-3xl font-semibold" :class="active ? 'text-white' : 'text-blue'">{{ title }}</div>
			<div v-if="description" class="text-slate-500 font-light mt-4" :class="active ? 'text-white' : 'text-slate-500'">{{ description }}</div>
			<div class="flex flex-col items-center gap-6 mt-auto">
				<div v-if="cost" class="text-2xl lg:text-2xl 2xl:text-3xl font-semibold" :class="active ? 'text-white' : 'text-blue'">{{ cost }}</div>
				<!-- Test with regular button first -->
				<button
					@click="handleClick"
					class="px-4 py-2 rounded text-white font-semibold"
					:class="active ? 'bg-white text-blue-600' : 'bg-blue-600 hover:bg-blue-700'"
				>
					{{ ctaLabel || 'Get this' }}
				</button>

				<!-- Original DsButton (commented out for testing)
				<DsButton
					:label="ctaLabel || 'Get this'"
					:type="active ? 'secondary' : 'primary'"
					:url="ctaUrl"
					@click="handleClick"
				/>
				-->
				<div class="flex flex-col items-center gap-2">
					<a @click="showMoreInfo = !showMoreInfo" class="flex items-center gap-2 cursor-pointer" :class="active ? 'text-white' : 'text-slate-500'">
						<span class="text-sm font-light">{{ toggleMoreText }}</span>
						<span class="material-symbols-outlined text-xl">{{ toggleMoreIcon }}</span>
					</a>
					<div :class="active ? 'text-white' : 'text-slate-500'">
						<slot v-if="showMoreInfo" name="moreInfo"></slot>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TestPackageCard',
	props: {
		title: String,
		description: String,
		cost: String,
		ctaLabel: String,
		ctaUrl: String,
		ribbonText: String,
		ribbonIcon: String,
		active: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			showMoreInfo: false
		}
	},
	computed: {
		toggleMoreText() {
			return this.showMoreInfo ? 'Less Info' : 'More Info'
		},
		toggleMoreIcon() {
			return this.showMoreInfo ? 'keyboard_arrow_up' : 'keyboard_arrow_down'
		},
		cardClasses() {
			console.log(`TestPackageCard [${this.title?.substring(0, 30)}] - active:`, this.active, 'returning classes:', this.active ? 'border-blue bg-blue' : 'border-slate-300 bg-white');
			return this.active ? 'border-blue bg-blue' : 'border-slate-300 bg-white'
		}
	},
	watch: {
		active: {
			handler(newValue, oldValue) {
				console.log(`TestPackageCard [${this.title?.substring(0, 30)}] - active prop changed from ${oldValue} to ${newValue}`);
			},
			immediate: true
		},
		title: {
			handler(newValue) {
				console.log(`TestPackageCard - title prop:`, newValue?.substring(0, 50));
			},
			immediate: true
		}
	},
	mounted() {
		console.log(`TestPackageCard mounted - title: ${this.title?.substring(0, 30)}, active: ${this.active}`);
	},
	methods: {
		handleClick(event) {
			console.log(`TestPackageCard [${this.title?.substring(0, 30)}] - button clicked, active: ${this.active}`);
			console.log('TestPackageCard - About to emit buttonClicked event');

			if (!this.ctaUrl || this.ctaUrl === '#') {
				event.preventDefault()
			}

			// Emit the event
			this.$emit('buttonClicked')
			console.log('TestPackageCard - buttonClicked event emitted');
		}
	}
}
</script>

<style scoped>
.c-package-card {
	transition: all 0.3s ease;
}

/* Add some basic styling for the blue theme */
.border-blue {
	border-color: #3b82f6;
}

.bg-blue {
	background-color: #3b82f6;
}

.text-blue {
	color: #3b82f6;
}

.border-slate-300 {
	border-color: #cbd5e1;
}

.bg-white {
	background-color: white;
}

.text-slate-500 {
	color: #64748b;
}
</style>
