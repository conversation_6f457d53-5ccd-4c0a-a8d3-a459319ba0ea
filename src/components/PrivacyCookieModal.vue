<template>
	<div style="display: contents" v-if="getAllowedCookiePolicy !== 'allowed'">
		<div class="cookie-modal-overlay"></div>
		<div class="cookie-modal">
			<div class="cookie-modal__header">
				<span>YOUR POLICY</span>
			</div>
			<div class="cookie-modal__wrapper">
				<div class="cookie-modal__body">
					<span>
						By continuing the use of our Website, you are explicitly and actively
						consenting to our processing of your personal information as per our <a
							@click="$router.push('privacy-policy')">Privacy Policy</a>.
					</span>
				</div>
				<div class="cookie-modal__submit">
					<button class="button is-primary" @click="acceptCookie">ACCEPT</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			showCookieModal: false
		};
	},
	computed: {
		getAllowedCookiePolicy() {
			return this.$store.getters.getAllowedCookiePolicy;
		}
	},
	methods: {
		acceptCookie() {
			this.$store.commit('setAllowedCookiePolicy');
		}
	},
};
</script>

<style lang="scss">
</style>