<template>
	<nav 
		ref="navRef"
		:class="['py-4 relative w-full transition-all', navClasses]"
	>
		<div class="flex items-center justify-between gap-8 px-8">
			<div class="flex gap-8 items-center">
				<template v-for="parentNavItem in navigation">
					<a
						href="#"
						@click.prevent.stop="setActiveSubmenu(parentNavItem)"
						class="text-stone-800 font-semibold transition-all flex items-center gap-2 hover:text-blue"
					>
						{{ parentNavItem.attributes.title }}
						<span
							v-if="parentNavItem.attributes.children.data.length > 0"
							:class="activeSubmenu === parentNavItem.id ? 'rotate-0' : ''"
							class="material-symbols-outlined text-base shrink-0 rotate-180"
						>keyboard_arrow_down</span>
					</a>
				</template>
			</div>
			<div class="flex gap-4 items-center">		
				<router-link
					to="/contact-us"
					class="text-digital-black font-semibold transition-all flex items-center gap-2 hover:text-blue"
				>
					Contact Us
				</router-link>
			</div>
		</div>

		<template 
			v-for="(parentNavItem, index) in navigation" 
			:key="index"
		>
			<div v-if="parentNavItem.attributes.children.data.length > 0 && activeSubmenu === parentNavItem.id" class="absolute w-full top-9 left-0 z-20 bg-white shadow-lg rounded-b-3xl pt-10 overflow-hidden">
				<div class="grid grid-cols-2">
					<div class="grid p-8" :class="`grid-cols-${parentNavItem.attributes.children.data.length}`">
						<div 
							v-for="(subNavSection, index) in parentNavItem.attributes.children.data" 
							:key="index" 
						>
							<div class="text-sky-500 text-sm font-medium mb-8">{{ subNavSection.attributes.title }}</div>
							<div v-if="subNavSection.attributes.children.data.length > 0" class="flex flex-col gap-8">
								<AppLink
									:to="subNavItem.attributes.url"
									v-for="subNavItem in subNavSection.attributes.children.data" 
									:key="subNavItem.id"
								>
									<div class="text-stone-800 text-base font-semibold capitalize flex items-center gap-2 hover:text-blue">
										<span v-if="subNavItem.attributes.icon" class="material-symbols-outlined text-sky-500 text-xl">{{ subNavItem.attributes.icon }}</span>
										{{ subNavItem.attributes.title }}
									</div>
								</AppLink>
							</div>
						</div>
					</div>
					<div class="bg-slate-50 p-8 grid grid-cols-2 gap-3">
						<div class="">
							<div class="text-sky-500 text-sm font-medium mb-8">Find Your Fibre</div>
							<router-link to="/feasibility-search" class="text-stone-800 text-base font-semibold capitalize flex items-center gap-2 mb-3 hover:text-blue transition-colors">
								Check My Area Coverage
								<span class="material-symbols-outlined text-xl">arrow_right_alt</span>
							</router-link>
							<div class="text-slate-500 text-sm font-light">Your address will inform us of the options available and get us started on your journey with Cybersmart.</div>
						</div>
						<div>
							<router-link to="/feasibility-search">
								<img class="w-full h-full object-cover rounded-xl" src="../assets/search-map.jpg" alt="A map">
							</router-link>
						</div>
					</div>
				</div>
			</div>
		</template>
	</nav>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import AppLink from '@/components/AppLink.vue'

const store = useStore()
const router = useRouter()
const navRef = ref<HTMLElement | null>(null)

const activeSubmenu = computed(() => store.state.activeSubmenu)

interface Props {
	navigation: Navigation[]
}

defineProps<Props>()

const setActiveSubmenu = (navItem: NavigationItem) => {
	if (navItem.attributes.children.data.length > 0) {
		store.commit('setActiveSubmenu', store.state.activeSubmenu == navItem.id ? null : navItem.id)
	}
	else {
		if (navItem.attributes.url.startsWith('http')) {
			window.open(navItem.attributes.url, '_blank');
		} else {
			router.push(navItem.attributes.url);
		}
	}
}

const handleClickOutside = (event: MouseEvent) => {
	if (navRef.value && !navRef.value.contains(event.target as Node) && activeSubmenu.value) {
		store.commit('setActiveSubmenu', null)
	}
}

onMounted(() => {
	document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
	document.removeEventListener('click', handleClickOutside)
})

const navClasses = computed(() => {
	var openNavBg, closedNavBg
	//Set the navigation background colour based on dark mode for navigation
	if (store.state.darkNavigation || store.state.darkMenu) {
		openNavBg = 'bg-white'
		closedNavBg = 'bg-white'
	}
	else {
		openNavBg = 'bg-white'
		closedNavBg = 'bg-slate-50'
	}
	if (activeSubmenu.value) {
		return `${openNavBg} rounded-3xl shadow-lg`
	}
	return `${closedNavBg} rounded-full`
})
</script>