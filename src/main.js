import { createApp, nextTick } from "vue";
import axios from "axios";
import VueAxios from "vue-axios";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import VueGoogleMaps from 'vue-google-maps-community-fork'
import './styles/app.css';
import { createHead } from '@unhead/vue'

import { registerComponents } from 'design-system';
import '../node_modules/design-system/dist/styles/style.css';

const DOMAINS = Object.keys(JSON.parse(import.meta.env.VITE_BRAND_ID_MAP));

function getDomainAndSubdomain(url) {
	let match = DOMAINS.find(d => url.includes(d)) || null;
	let subdomain = null;
	if (match) {
		let index = url.indexOf(match);
		subdomain = index > 0 ? url.substring(0, index - 1) : null;
	}
	return { domain: match, subdomain };
}

let { domain, subdomain } = getDomainAndSubdomain(window.location.host);

function getBrandId(domain) {
	const map = JSON.parse(import.meta.env.VITE_BRAND_ID_MAP);
	let brandId = map[domain];
	if (brandId === undefined) {
		brandId = "";
	}
	return brandId;
}

// if (subdomain) {
// 	const brandId = getBrandId(domain);
// 	const zonecode = subdomain + brandId;
// 	const protocol = window.location.protocol;
// 	const suffix = /^\/a\/[a-zA-Z0-9]+$/.test(window.location.pathname) && window.location.pathname || "";
// 	if (["cybersmart.co.za", "lightspeed.co.za"].includes(domain)) {
// 		domain = "www." + domain;
// 	} else if (["ftta.co.za"].includes(domain)) {
// 		domain = "buildings." + domain;
// 	}
// 	window.location = `${protocol}//${domain}/location/${zonecode}${suffix}`;
// }

// Create Vue app
const app = createApp(App);

//Ensure form components work using the vue 3 method
// configureCompat({
// 	COMPONENT_V_MODEL: false,
// 	ATTR_FALSE_VALUE: false,
// 	WATCH_ARRAY: false
// })

// Register global components and plugins
app.use(VueAxios, axios);
app.use(store);
app.use(router);
//Google maps config
app.use(VueGoogleMaps, {
    load: {
        key: import.meta.env.VITE_MAPS_API_KEY,
		libraries: "places",
		loading: 'async'
    },
})

registerComponents(app);

//SEO
const head = createHead()
app.use(head)

// Mount app
app.mount("#app");

// Dispatch app-rendered event
nextTick(() => {
	document.dispatchEvent(new Event("x-app-rendered"));
});
