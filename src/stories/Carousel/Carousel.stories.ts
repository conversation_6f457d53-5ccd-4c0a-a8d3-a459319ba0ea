import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import Carousel from './Carousel.vue';

const meta: Meta<typeof Carousel> = {
	title: 'UI Components/Carousel',
	component: Carousel,
	args: {
		images: [
			{
				alt: 'The sun shines on the mountains and trees (by <PERSON> on Unsplash)',
				url: 'http://unsplash.it/1024/800?gravity=center',
			},
			{
				alt: 'A waterfall in the middle of a forest (by <PERSON> on Unsplash)',
				url: 'http://unsplash.it/1024/800?random',
			},
			{
				alt: 'The sun is setting over a lavender field (by <PERSON> on Unsplash)',
				url: 'http://unsplash.it/1024/800?gravity=center',
			},
			{
				alt: 'A field of grass with the sun setting in the background (by <PERSON><PERSON> on Unsplash)',
				url: 'http://unsplash.it/1024/800?random',
			},
		]
	},
	argTypes: {
		slidesPerPage: { control: 'select', options: [1, 2, 3, 4] },
	},
	tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof Carousel>;

export const Default: Story = {
	args: {
	},
	render: (args) => ({
		components: { Carousel },
		setup() {
			return { args };
		},
		template: `
		<Carousel v-bind="args"></Carousel>`,
	}),
};