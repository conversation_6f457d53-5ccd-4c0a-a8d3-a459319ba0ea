"use strict";

/**
 * feasibility service
 */

const { createCoreService } = require("@strapi/strapi").factories;
const { CONTENT_TYPE_ID } = require("../const");
const moment = require("moment");
const EXPIRE_TIME = process.env.CACHE_EXPIRE_TIME || 60;

module.exports = createCoreService(CONTENT_TYPE_ID, ({strapi}) => ({
  async checkCache(key, content, expiredId) {
    let entity, expired;

    if (content && expiredId) {
      entity = await super.update(expiredId, {
        data: {
          cacheKey: key,
          cacheContent: content,
        }
      })
    } else if (content) {
      entity = await super.create({
        data: {
          cacheKey: key,
          cacheContent: content,
        }
      })
    } else {
      const { results } = await super.find({
        pagination: {
          limit: -1
        },
        sort: "updatedAt:desc",
        filters: {
          cacheKey: key,
        }
      })

      results.forEach((item) => {
        if (moment().diff(item.updatedAt, "minutes") < EXPIRE_TIME) {
          entity = item;
        } else {
          expired = item.id;
        }
      });
    }

    return expired ? {expired: expired} : (entity ? JSON.parse(entity.cacheContent) : null);
  }
}));
