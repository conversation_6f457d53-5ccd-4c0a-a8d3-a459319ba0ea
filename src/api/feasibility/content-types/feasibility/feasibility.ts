// Interface automatically generated by schemas-to-ts

export interface Feasibility {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    cacheKey?: string;
    cacheContent?: string;
  };
}
export interface Feasibility_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  cacheKey?: string;
  cacheContent?: string;
}

export interface Feasibility_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  cacheKey?: string;
  cacheContent?: string;
}

export interface Feasibility_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  cacheKey?: string;
  cacheContent?: string;
}
