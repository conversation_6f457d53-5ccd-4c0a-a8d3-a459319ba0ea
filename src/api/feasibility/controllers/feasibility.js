"use strict";

/**
 * feasibility controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const { CONTENT_TYPE_ID } = require("../const");
const util = require("util");
const rp = require("request-promise");
const querystring = require("querystring");
const telegramSend = require("../../../../utils/telegramSend");


const BRANDS = {
  CYBERSMART: "Cybersmart",
  FTTA: "FTTA",
  GIGALIGHT: "Gigalight",
}

const ALLOWED_STATUS = [
  "Active",
  "Active Wireless",
  "In-Progress",
  "Pre-Order",
  "Waiting Consent",
  "Waiting Reticulation",
  "Installing",
];
const STATUS_IN_PROGRESS = [
  "In-Progress",
  "Waiting Consent",
  "Waiting Reticulation",
];
const STATUS_ACTIVE = [
  "Active",
  "Installing",
];

const MAIN_PROVIDERS = {
  CYBERSMART: {
    "type": "Cybersmart",
    "id": "1"
  },
  FTTA: {
    "type": "FTTA",
    "id": "12"
  },
  OPENSERVE_DSL: {
    "type": "Openserve",
    "id": "12"
  },
}

const SEARCH_AREA_TYPES = {
  PLANNED: "Planned",
  LIVE: "Live"
}

const LOG_TYPES = {
  FEASIBILITY_CHECK: "feasibility check",
}

const FEASIBILITY_API_URL = strapi.config.feasibility.baseUrl;
const FEASIBILITY_API_TOKEN = strapi.config.feasibility.createZoneRecordAPIKey;

const CONTENT_TYPE_PROVIDERS_ID = "api::providers-list.providers-list";

module.exports = createCoreController(CONTENT_TYPE_ID, ({ strapi}) => ({
  async find(ctx) {
    console.log("Pre Feasibility Check:", util.inspect(ctx.request.body, false, 2));
    const { latitude, longitude } = ctx.request.body;

    if (latitude === undefined || longitude === undefined) {
      ctx.response.status = 400;
      ctx.response.body = ctx.response.message =
        "Needs both latitude and longitude";
      return;
    }
    let feasibilityCache;
    await strapi.plugins["gps-extract"]?.services.myLogService.create({
      type: LOG_TYPES["FEASIBILITY_CHECK"],
      lat: latitude,
      long: longitude,
      dateC: new Date()
    });
    try {
      feasibilityCache = await strapi.service(CONTENT_TYPE_ID).checkCache(`feasibility_${latitude}${longitude}`);
      console.log("Check feasibility Cache", util.inspect(feasibilityCache, false, 1));

      if (feasibilityCache && !feasibilityCache.expired && feasibilityCache.length) {
        return ctx.send(feasibilityCache);
      }
    } catch (e) {
      console.error("Feasibility Check Error::", e);
      return ctx.send("Something broke, try again later", 500);
    }

    try {
      let response = [];
      const brand = getUserBrand(ctx.header.origin);

      const liveAndPlannedFilter = [
        brand === BRANDS.FTTA ? MAIN_PROVIDERS.FTTA : MAIN_PROVIDERS.CYBERSMART
      ]

      response = await getFeasibility(longitude, latitude, brand, SEARCH_AREA_TYPES.LIVE, liveAndPlannedFilter, true);

      if (!response.length || Number(response[0].provider_id) === Number(MAIN_PROVIDERS.OPENSERVE_DSL.id)) {
        let feasibilityResponsePlanned = [];

        if (brand !== BRANDS.FTTA) {
          feasibilityResponsePlanned = await getFeasibility(longitude, latitude, brand, SEARCH_AREA_TYPES.PLANNED, liveAndPlannedFilter);
        }

        const feasibilityResponseOtherProviders = await getOtherProviders(longitude, latitude, brand);
        const combinedArray = feasibilityResponsePlanned
          .filter(provider => Number(provider.provider_id) !== Number(MAIN_PROVIDERS.OPENSERVE_DSL.id))
          .concat(feasibilityResponseOtherProviders);
        const uniqueProviders = new Set();


        response = combinedArray.filter((item) => {
          if (uniqueProviders.has(item.provider_name)) {
            return false;
          }
          uniqueProviders.add(item.provider_name);
          return true;
        });
      }

      feasibilityCache = await strapi.service(CONTENT_TYPE_ID).checkCache(`feasibility_${latitude}${longitude}`,
        JSON.stringify(response), (feasibilityCache && feasibilityCache.expired ? feasibilityCache.expired : null));
      console.log("stored filteredZones to cache", util.inspect(feasibilityCache, false, 2));

      return ctx.send(feasibilityCache);
    } catch (e) {
      console.error("Get node Info Error::", e);
      return ctx.send( "Something broke, try again later", 500);
    }
  },
  async lookup(ctx) {
    const { zone } = ctx.request.body;

    if (zone === undefined) {
      console.error("Missing the zone code");
      ctx.response.status = 400;
      ctx.response.body = ctx.response.message = "Missing the zone code";
      return;
    }

    // Try get from caching first
    let lookupCache;
    try {
      lookupCache = await strapi.service(CONTENT_TYPE_ID).checkCache(`lookup_${zone}`);
      console.log("Check lookup Cache",  util.inspect({...lookupCache, image: "<removed>"}, false, 2));

      if (lookupCache && !lookupCache.expired) {
        lookupCache.promoCodes = changeLookupProductsForV2(lookupCache);
        return ctx.send(lookupCache);
      }
    } catch (e) {
      console.error("Feasibility Check Error::", e);
      return ctx.send("Something broke, try again later", 500);
    }

    // Get Node info
    try {
      const post = await rp({
        method: "POST",
        url: `${FEASIBILITY_API_URL}/fibre/nodeinfo`,
        headers: {
          authKey: FEASIBILITY_API_TOKEN,
        },
        form: {
          distributionNodeCode: zone,
          showUnits: 1,
          showImage: 1,
        },
      });

      let zoneInfoResponse = JSON.parse(post);
      let toPrint = {...zoneInfoResponse};
      toPrint["image"] = "[DELETED]";
      console.log("Get zone Info Response:", util.inspect(toPrint, false, 2));

      // Get Node Addresses
      try {
        const post = await rp({
          method: "POST",
          url: `${FEASIBILITY_API_URL}/fibre/nodeunits`,
          headers: {
            authKey: FEASIBILITY_API_TOKEN,
          },
          form: {
            distributionNodeCode: zone
          },
        });

        let addressResponse = JSON.parse(post);

        // Add / Update result to caching
        lookupCache = await strapi.service(CONTENT_TYPE_ID).checkCache(`lookup_${zone}`,
          JSON.stringify({...zoneInfoResponse, ...addressResponse}),
          (lookupCache && lookupCache.expired ? lookupCache.expired : null));
        console.log("stored lookupResponse to cache", util.inspect(lookupCache, false, 2));
        lookupCache.promoCodes = changeLookupProductsForV2(lookupCache);
        return ctx.send(lookupCache);
      } catch (e) {
        console.error("Get node addresses Error::", e);
        return ctx.send("Something broke, try again later", 500);
      }

    } catch (e) {
      console.error("Get node Info Error::", e);
      return ctx.send("Something broke, try again later", 500);
    }
  },
  async productDetails(ctx) {
    const { promoCode } = ctx.request.body;

    if (!promoCode) {
      const errorMessage = "Missing the Promo Code Id";
      console.error(errorMessage);
      return ctx.send(errorMessage, 400);
    }

    const productDetailsApiToken = strapi.config.feasibility.productDetails.authKey;
    // Get Node info
    try {
      const post = await rp({
        method: "POST",
        url: `${FEASIBILITY_API_URL}api/promocode/get_promo_product_details`,
        headers: {
          authKey: productDetailsApiToken,
        },
        form: {
          promoCode: promoCode
        },
      });

      let response = JSON.parse(post);
      console.log("Get product details Response:", util.inspect(response, false, 2));
      return ctx.send(response);

    } catch (e) {
      console.error("Get product details Error::", e);
      return ctx.send("Something broke, try again later", 500);
    }
  }
}));



function filterZones(feasibilityResponse) {
  // Don"t show cybersmart zones with not allowed status (e.g. "Cancelled")
  let filteredZones = feasibilityResponse.filter(zone =>
    zone.provider_name !== "Cybersmart" || ALLOWED_STATUS.includes(zone.zone_status));
  //console.log("filteredZones: ", filteredZones);

  return getActiveIfPresentOtherwisePutInProgressFirst(filteredZones);
}

function getActiveIfPresentOtherwisePutInProgressFirst(zones) {
  let activeZones = zones.filter(zone =>
    zone.provider_name.includes("Cybersmart") && STATUS_ACTIVE.includes(zone.zone_status));

  if (activeZones.length) {
    // Don"t show other zones if there is a Active Cybersmart zone
    return activeZones;
  } else {
    let inProgressZones = zones.filter(zone => zone.provider_name.includes("Cybersmart") &&
      STATUS_IN_PROGRESS.includes(zone.zone_status));
    let otherZones =      zones.filter(zone => !(zone.provider_name.includes("Cybersmart") &&
      STATUS_IN_PROGRESS.includes(zone.zone_status)));

    let inProgressInName = inProgressZones.filter(zone => zone.provider_name.includes("Pre-Order"));
    if (inProgressInName.length) {
      // If any Pre-Order zones, then show only those
      inProgressZones = inProgressInName;
    }

    // Sort In-Progress zones to start of the list
    return [...inProgressZones, ...otherZones];
  }
}

function getTempBody(longitude, latitude, brand, type = null, neededRange = false) {
  let body = {
    longitude,
    latitude,
    showUnits: 1,
    showKml: type === SEARCH_AREA_TYPES["PLANNED"] || type === SEARCH_AREA_TYPES["LIVE"] ? 1 : 0,
    showImage: 1,
    brand: brand !== BRANDS.FTTA ? BRANDS.CYBERSMART : BRANDS.FTTA
  }
  if (neededRange) {
    const rangeKM = strapi.config.feasibility.feasibility.rangeKm;
    if (rangeKM) {
      body.rangeKm = rangeKM;
    }
  }
  if (type) {
    body.searchAreaType = type;
  }

  return body;
}

async function getFeasibility(longitude, latitude, brand, areaType = null, filter, neededRange = false) {
  const options = {
    method: "POST",
    url: `${FEASIBILITY_API_URL}/fibre/v2/feasibility?${querystring.encode(getTempBody(longitude, latitude, brand, areaType, neededRange))}`,
    headers: {
      authKey: FEASIBILITY_API_TOKEN,
    },
    json: {
      "provider_filter": filter
    }
  }
  let response =  await rp(options);

  if (areaType) {
    const firstZone = response.find(zone => ALLOWED_STATUS.includes(zone.zone_status));
    response = firstZone ? [firstZone] : [];
    if (response.length && areaType === SEARCH_AREA_TYPES.PLANNED && Number(response[0].provider_id) === Number(MAIN_PROVIDERS.CYBERSMART.id)) {
      response[0].preOrder = true;
    }
  }
  return response;
}

function getUserBrand(origin) {
  const brandMap = JSON.parse(process.env.DOMAIN_TO_BRAND_ID_MAP);
  let brand = brandMap[origin];

  if (brand === undefined || brand === "") {
    brand = "Cybersmart";
  }

  console.log("Determine brandId", brandMap, origin, brand);

  return brand;
}
function changeLookupProductsForV2(object) {
  let products = {};
  for (let key in object.promoCodes) {
    let promoCode = object.promoCodes[key];
    products[key] = {
      id: promoCode.id,
      code: promoCode.code,
      description: promoCode.description,
      promocodenotes: promoCode.promocodenotes,
      providers: promoCode.providers,
      products: promoCode.products.map(product => ({
        promocodeproductid: product.promocodeproductid,
        main_product: [{
          code: product.code,
          name: product.name,
          description: product.description,
          details: product.details,
          monthly: product.monthly,
          duration: product.duration,
          productid: product.productid,
          setup: product.setup
        }]
      }))
    }
  }
  return products;
}

async function getOtherProviders(longitude, latitude, brand) {
  let response = [];
  const { results } = await strapi.service(CONTENT_TYPE_PROVIDERS_ID).find({
    filters: {
      active: true
    }
  });
  const otherProvidersForBrand = results
    .filter(provider => provider.providerBrand === brand)
    .map(provider => {
      return {
        type: provider.providerName,
        id: provider.providerId
      }
    });

  try {
    response = await getFeasibility(longitude, latitude, brand, null, otherProvidersForBrand);
  } catch (err) {
    const token = strapi.config.telegram.exceptionBot.token;
    const chatId = strapi.config.telegram.exceptionBot.chatId;
    await telegramSend.send(chatId, `
    Feasibility Error: ${err.message} (getOtherProviders)
    url: ${err.options.url}
    json: ${err.options.json ? JSON.stringify(err.options.json) : ""}`,
      token);
  }

  return response;
}
