{"kind": "collectionType", "collectionName": "resource_categories", "info": {"singularName": "resource-category", "pluralName": "resource-categories", "displayName": "ResourceCategory", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string", "required": true}, "icon": {"type": "string", "required": true}, "resource_items": {"type": "relation", "relation": "oneToMany", "target": "api::resource-item.resource-item", "mappedBy": "resource_category"}}}