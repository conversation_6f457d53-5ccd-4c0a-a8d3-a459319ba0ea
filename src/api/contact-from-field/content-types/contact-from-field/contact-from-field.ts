// Interface automatically generated by schemas-to-ts

import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export enum InputType {
  Text = 'text',
  Number = 'number',
  Tel = 'tel',
  Hidden = 'hidden',
  Textarea = 'textarea',
  Select = 'select',
  Radio = 'radio',
  Checkbox = 'checkbox',
  Bar = 'bar',}

export interface ContactFromField {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    optionText: string;
    inputName?: string;
    inputOptions?: string;
    conditionalValue?: string;
    defaultValue?: string;
    placeholder?: string;
    inputType: InputType;
    required?: boolean;
    conditionalField?: { data: ContactFromField };
    priority?: number;
  };
}
export interface ContactFromField_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  optionText: string;
  inputName?: string;
  inputOptions?: string;
  conditionalValue?: string;
  defaultValue?: string;
  placeholder?: string;
  inputType: InputType;
  required?: boolean;
  conditionalField?: ContactFromField_Plain;
  priority?: number;
}

export interface ContactFromField_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  optionText: string;
  inputName?: string;
  inputOptions?: string;
  conditionalValue?: string;
  defaultValue?: string;
  placeholder?: string;
  inputType: InputType;
  required?: boolean;
  conditionalField?: number;
  priority?: number;
}

export interface ContactFromField_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  optionText: string;
  inputName?: string;
  inputOptions?: string;
  conditionalValue?: string;
  defaultValue?: string;
  placeholder?: string;
  inputType: InputType;
  required?: boolean;
  conditionalField?: AdminPanelRelationPropertyModification<ContactFromField_Plain>;
  priority?: number;
}
