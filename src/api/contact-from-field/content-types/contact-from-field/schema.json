{"kind": "collectionType", "collectionName": "contact_from_fields", "info": {"singularName": "contact-from-field", "pluralName": "contact-from-fields", "displayName": "contactFromField", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"optionText": {"type": "string", "required": true}, "inputName": {"type": "string"}, "inputOptions": {"type": "string"}, "conditionalValue": {"type": "string"}, "defaultValue": {"type": "string"}, "placeholder": {"type": "string"}, "inputType": {"type": "enumeration", "enum": ["text", "number", "tel", "hidden", "textarea", "select", "radio", "checkbox", "bar"], "required": true}, "required": {"type": "boolean"}, "conditionalField": {"type": "relation", "relation": "oneToOne", "target": "api::contact-from-field.contact-from-field"}, "priority": {"type": "integer"}}}