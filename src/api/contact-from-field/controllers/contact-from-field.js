"use strict";

/**
 * contact-from-field controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const { CONTENT_TYPE_ID } = require("../const");

module.exports = createCoreController(CONTENT_TYPE_ID, ({ strapi }) => ({
  async find(ctx) {
    const { results } = await strapi.service(CONTENT_TYPE_ID).find({
      pagination: {
        limit: -1
      },
      populate: "conditionalField"
    });
    return results || [];
  }
}));
