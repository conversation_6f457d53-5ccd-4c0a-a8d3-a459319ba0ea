"use strict";

/**
 * contact-email controller
 */
const rp = require("request-promise");

const { createCoreController } = require("@strapi/strapi").factories;
const { CONTENT_TYPE_ID } = require("../const");
const GOOGLE_RECAPTCHA_SECRET = process.env.GOOGLE_RECAPTCHA_SECRET;

module.exports = createCoreController(CONTENT_TYPE_ID, ({ strapi }) => ({
  async createRequest(ctx) {
    console.log(`Create Contact-us Request Fields: ${JSON.stringify(ctx.request.body)}`);
    const customerEmail = ctx.request.body["email-address"] ?
      {from: ctx.request.body["email-address"]}: {};

    const recaptcha = JSON.parse(await rp({
      method: "POST",
      url: "https://www.google.com/recaptcha/api/siteverify",
      form: {
        secret: GOOGLE_RECAPTCHA_SECRET,
        response: ctx.request.body.recaptcha_token,
      },
    }));


    if (recaptcha.success) {
      const { results: contactEmails } = await strapi.service(CONTENT_TYPE_ID).find({
        pagination: {
          limit: -1
        }
      });

      let emailToSend = {};
      console.log(`Start send contact mail. Contact e-mails found:  ${contactEmails.length}`);
      emailToSend = contactEmails.map((email) => {
        let match = true;
        let queries = email.query.split(";");
        queries.forEach((query) => {
          let queryTerm = query.trim().split("=");
          let field = queryTerm[0] ? queryTerm[0].trim().replace("[", "").replace("]", "") : "";
          let searchTerm = queryTerm[1] ? queryTerm[1].trim().replace(/'/g, "")  : "";
          if (ctx.request.body[field] !== searchTerm) {
            match = false;
          }
        })
        if (match === true) {
          let emailDetails = {};
          emailDetails.toEmail = email["toEmail"];
          let matches = email["messageContent"].match(/\[(.*?)\]/g);
          let emailBody = email["messageContent"];

          if (matches) {
            matches.forEach((match) => {
              let rex = new RegExp("\\[" + match.trim().replace("[", "").replace("]", "") + "]", "g");
              emailBody = emailBody.replace(rex, ctx.request.body[match.trim().replace("[", "").replace("]", "")]);
            });
          }
          emailDetails.message = emailBody;

          matches = email["subject"].match(/\[(.*?)\]/g);
          let subject = email["subject"];

          if (matches) {
            matches.forEach((match) => {
              let rex = new RegExp("\\[" + match.trim().replace("[", "").replace("]", "") + "]", "g");
              subject = subject.replace(rex, ctx.request.body[match.trim().replace("[", "").replace("]", "")]);
            });
          }
          emailDetails.subject = subject;
          return emailDetails;
        }
      });
      const applicableEmails = emailToSend.filter(email => email);

      try {
        for (const item of applicableEmails) {
          let showdown = require("showdown"),
            converter = new showdown.Converter(),
            html = await converter.makeHtml(item.message);

          const sendParams = {
            ...customerEmail,
            to: item.toEmail,
            subject: item.subject
          };

          const emailSent = await strapi.plugins["email"].services.email.send({
            ...sendParams,
            html: html
          });

          console.log(`Email contact sent ${JSON.stringify(emailSent)}`);
        }
      } catch (e) {
        console.error(`Email contact error:: ${JSON.stringify(e)}`);
      }
      return ctx.send("Done");
    } else {
      console.error("Recaptcha failed.");
      return ctx.send("Recaptcha failed.", 500);
    }
  }
}));
