// Interface automatically generated by schemas-to-ts

export interface ContactEmail {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    subject?: string;
    toEmail?: string;
    messageContent?: string;
    query?: string;
  };
}
export interface ContactEmail_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  subject?: string;
  toEmail?: string;
  messageContent?: string;
  query?: string;
}

export interface ContactEmail_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  subject?: string;
  toEmail?: string;
  messageContent?: string;
  query?: string;
}

export interface ContactEmail_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  subject?: string;
  toEmail?: string;
  messageContent?: string;
  query?: string;
}
