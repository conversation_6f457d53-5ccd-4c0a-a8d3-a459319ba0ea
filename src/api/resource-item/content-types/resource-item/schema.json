{"kind": "collectionType", "collectionName": "resource_items", "info": {"singularName": "resource-item", "pluralName": "resource-items", "displayName": "ResourceItem", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"label": {"type": "string"}, "file": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["files"]}, "resource_category": {"type": "relation", "relation": "manyToOne", "target": "api::resource-category.resource-category", "inversedBy": "resource_items"}}}