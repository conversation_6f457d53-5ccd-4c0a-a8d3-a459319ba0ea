// Interface automatically generated by schemas-to-ts

export interface Legal {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    category: string;
    name: string;
    description: string;
  };
}
export interface Legal_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  category: string;
  name: string;
  description: string;
}

export interface Legal_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  category: string;
  name: string;
  description: string;
}

export interface Legal_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  category: string;
  name: string;
  description: string;
}
