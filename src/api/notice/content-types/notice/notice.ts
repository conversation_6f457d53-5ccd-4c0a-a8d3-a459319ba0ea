// Interface automatically generated by schemas-to-ts

export interface Notice {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    title: string;
    description: string;
    revealDate?: Date;
    endDate?: Date;
  };
}
export interface Notice_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  title: string;
  description: string;
  revealDate?: Date;
  endDate?: Date;
}

export interface Notice_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  title: string;
  description: string;
  revealDate?: Date;
  endDate?: Date;
}

export interface Notice_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  title: string;
  description: string;
  revealDate?: Date;
  endDate?: Date;
}
