"use strict";

/**
 * blog controller
 */

const { createCoreController } = require("@strapi/strapi").factories;

const { CONTENT_TYPE_ID } = require("../const");

module.exports = createCoreController(CONTENT_TYPE_ID , ({ strapi }) =>  ({
  async find(ctx) {
    return await strapi.db.query(CONTENT_TYPE_ID).findMany({
      limit: 9,
      offset: 9 * parseInt(ctx.query._page || 0),
      sort: "revealDate:desc",
      where: {
        $not: {
          category: null
        },
        revealDate: {
          $lte: new Date()
        }
      },
      populate: ["feature", "category"]
    });
  },
  async findOne(ctx) {
    const blogId = ctx.request.params.id;

    if (isNaN(blogId)) {
      return null;
    }

    return strapi.service(CONTENT_TYPE_ID).findOne(blogId, {
      populate: ["feature", "category"]
    });
  },
  async byCategory(ctx) {
    let response = [];
    const params = ctx.query;

    if (params["id_ne"] &&  params["category.id"]) {
      const { results: response } = await strapi.service(CONTENT_TYPE_ID).find({
        pagination: {
          limit: 2,
        },
        sort: "revealDate:desc",
        filters: {
          id: { $not: params["id_ne"] },
          category: { id: params["category.id"] },
          $not: { category: null },
        },
        populate: ["feature", "category"]
      })
      return response;
    } else {
      return response;
    }
  }
}));
