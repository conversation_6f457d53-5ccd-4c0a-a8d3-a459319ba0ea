// Interface automatically generated by schemas-to-ts

import { Media } from '../../../../common/schemas-to-ts/Media';
import { Category } from '../../../category/content-types/category/category';
import { Seo } from '../../../../components/shared/interfaces/Seo';
import { Media_Plain } from '../../../../common/schemas-to-ts/Media';
import { Category_Plain } from '../../../category/content-types/category/category';
import { Seo_Plain } from '../../../../components/shared/interfaces/Seo';
import { Seo_NoRelations } from '../../../../components/shared/interfaces/Seo';
import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export interface Blog {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    title: string;
    revealDate?: Date;
    metaTitle: string;
    feature: { data: Media };
    category?: { data: Category };
    metaDescription: string;
    content: string;
    synopsis: string;
    seo?: Seo;
  };
}
export interface Blog_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  title: string;
  revealDate?: Date;
  metaTitle: string;
  feature: Media_Plain;
  category?: Category_Plain;
  metaDescription: string;
  content: string;
  synopsis: string;
  seo?: Seo_Plain;
}

export interface Blog_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  title: string;
  revealDate?: Date;
  metaTitle: string;
  feature: number;
  category?: number;
  metaDescription: string;
  content: string;
  synopsis: string;
  seo?: Seo_NoRelations;
}

export interface Blog_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  title: string;
  revealDate?: Date;
  metaTitle: string;
  feature: AdminPanelRelationPropertyModification<Media_Plain>;
  category?: AdminPanelRelationPropertyModification<Category_Plain>;
  metaDescription: string;
  content: string;
  synopsis: string;
  seo?: Seo_Plain;
}
