{"kind": "collectionType", "collectionName": "blogs", "info": {"singularName": "blog", "pluralName": "blogs", "displayName": "Blog", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "revealDate": {"type": "datetime"}, "metaTitle": {"type": "string", "required": true}, "feature": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "category": {"type": "relation", "relation": "oneToOne", "target": "api::category.category"}, "metaDescription": {"type": "text", "required": true}, "content": {"type": "text", "required": true}, "synopsis": {"type": "text", "required": true}}}