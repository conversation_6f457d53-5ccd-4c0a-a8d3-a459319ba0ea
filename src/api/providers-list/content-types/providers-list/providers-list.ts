// Interface automatically generated by schemas-to-ts

export interface ProvidersList {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    providerName: string;
    providerId: string;
    providerBrand: string;
    active?: boolean;
  };
}
export interface ProvidersList_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  providerName: string;
  providerId: string;
  providerBrand: string;
  active?: boolean;
}

export interface ProvidersList_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  providerName: string;
  providerId: string;
  providerBrand: string;
  active?: boolean;
}

export interface ProvidersList_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  providerName: string;
  providerId: string;
  providerBrand: string;
  active?: boolean;
}
