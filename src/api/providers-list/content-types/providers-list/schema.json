{"kind": "collectionType", "collectionName": "providers_lists", "info": {"singularName": "providers-list", "pluralName": "providers-lists", "displayName": "ProvidersList", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"providerName": {"type": "string", "required": true}, "providerId": {"type": "string", "required": true}, "providerBrand": {"type": "string", "required": true}, "active": {"type": "boolean", "default": false}}}