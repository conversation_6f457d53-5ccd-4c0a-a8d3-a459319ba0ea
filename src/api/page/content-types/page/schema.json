{"kind": "collectionType", "collectionName": "pages", "info": {"singularName": "page", "pluralName": "pages", "displayName": "Page", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "slug": {"type": "string", "required": true, "unique": true, "regex": "^[a-z0-9/-]+$"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo", "required": true}, "content": {"type": "dynamiczone", "components": ["blocks.section-intro", "blocks.cta-banner", "blocks.cta-card-grid", "blocks.feasibility-search", "blocks.feature-card-grid", "blocks.logo-banner", "blocks.link-card-grid", "blocks.hero", "blocks.testimonial-slider", "blocks.fa-qs", "blocks.testimonial-feature-slider", "blocks.business-package-comparison-table", "blocks.home-connectivity-hero", "blocks.home-connectivity-features", "blocks.image-feature", "blocks.stats-banner", "blocks.rich-content", "blocks.newsletter-signup-banner", "blocks.banner-link"], "required": true}, "darkNavigation": {"type": "boolean", "default": false}}}