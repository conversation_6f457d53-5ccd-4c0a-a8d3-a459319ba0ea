// Interface automatically generated by schemas-to-ts

import { Seo } from '../../../../components/shared/interfaces/Seo';
import { Seo_Plain } from '../../../../components/shared/interfaces/Seo';
import { Seo_NoRelations } from '../../../../components/shared/interfaces/Seo';

export interface Page {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    title: string;
    slug: string;
    seo?: Seo;
    content: any;
  };
}
export interface Page_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  title: string;
  slug: string;
  seo?: Seo_Plain;
  content: any;
}

export interface Page_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  title: string;
  slug: string;
  seo?: Seo_NoRelations;
  content: any;
}

export interface Page_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  title: string;
  slug: string;
  seo?: Seo_Plain;
  content: any;
}
