// Interface automatically generated by schemas-to-ts

import { TestimonialCard } from '../../../../components/components/interfaces/TestimonialCard';
import { TestimonialCard_Plain } from '../../../../components/components/interfaces/TestimonialCard';
import { TestimonialCard_NoRelations } from '../../../../components/components/interfaces/TestimonialCard';

export interface Testimonial {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    testimonial: TestimonialCard;
  };
}
export interface Testimonial_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  testimonial: TestimonialCard_Plain;
}

export interface Testimonial_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  testimonial: TestimonialCard_NoRelations;
}

export interface Testimonial_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  testimonial: TestimonialCard_Plain;
}
