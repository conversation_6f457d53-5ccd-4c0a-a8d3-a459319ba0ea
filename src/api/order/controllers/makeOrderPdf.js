"use strict";

const moment = require("moment");
const puppeteer = require("puppeteer");
const fs = require("fs").promises;
const { toBase64 } = require("request/lib/helpers");
const path = require("path");

const savePath = "./public/pdfs/orders/";
const colors = {
	blue: "#01a5e8",
	darkBlue: "#215E9E",
	black: "#231f20",
	sand: "#EDF1EC",
	white: "#fff",
};

module.exports = {
	async makePdf(data) {
		const orderTitle = `order_no_${data.orderNumber}_${moment().format("DD-MM-YYYY")}`;
		const packageTitle = data.product_details.name;
		const subTitle = "";
		let brand = data.brand || "cybersmart";
		let brandName = brand.toUpperCase();
		let termsUrl = null;
		let aupUrl = null;
		let logo = null;
		let footerText = null;

		if (brand === "gigalight") {  // Gigalight
			logo = "https://api.platinumseed.com/cybersmart-pdf-assets/gigalogo.png";
			termsUrl = "http://www.gigalight.co.za/assets/terms_gigalight.pdf";
			aupUrl = "http://www.gigalight.co.za/assets/aup_gigalight.pdf";
			footerText = "054 332 2290 | WWW.GIGALIGHT.CO.ZA";
		} if (brand === "ftta") { // FTTA
			logo = "https://api.platinumseed.com/cybersmart-pdf-assets/ftta-vector-logo.svg";
			termsUrl = "http://www.ftta.co.za/assets/terms_ftta.pdf";
			aupUrl = null;  // AUP is in terms and conditions
			footerText = "021 286 3882 | WWW.FTTA.CO.ZA";
		} else {  // Cybersmart
			logo = "https://api.platinumseed.com/cybersmart-pdf-assets/email_logo.svg"; // server
			termsUrl = "https://www.cybersmart.co.za/terms.pdf";
			aupUrl = "https://www.cybersmart.co.za/aup.pdf";
			footerText = "021 286 0123 | WWW.LIGHTSPEED.CO.ZA";
		}

		// HTML and styling remain the same as in the original code
		const htmlHead = `
    	<head>
			<meta charset="utf8">
			<title>${packageTitle}</title>

			<link rel="preconnect" href="https://fonts.gstatic.com">
			<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;500&display=swap" rel="stylesheet">

			<style>
				html,body {
				fon												t-family: "Roboto", sans-serif;
				font-size: 7px;
				line-height: 10px;
				font-weight: lighter;
				text-transform: uppercase;
				color: ${colors.black};
				}

				* {
				box-sizing: border-box;
				}

				.page-wrap {
				clear:both;
				width: 100%;
				height: 630px;
				}

				.page-break {
				page-break-after: always;
				}

				.flex-grid {
				clear:both;
				}
				.col-half {
				width:50%;
				}
				.col-third {
				width:33.33333%;
				}
				.col-full {
				width:100%;
				}

				.col-half, .col-third, .col-full {
				padding: 2px 10px;
				float:left
				}

				img {
				max-width:100%;
				height:auto;
				}

				.border {
				border: solid 1px ${colors.black};
				}

				.title-strip {
				display: block;
				background-color: ${colors.blue};
				color: ${colors.white};
				font-size: 22px;
				line-height: 22px;
				font-weight: normal;
				height:65px;
				margin:0;
				margin-top:30px;
				padding:10px 15px;
				}

				.title-sub {
				display: block;
				color: ${colors.black};
				font-size: 22px;
				line-height: 22px;
				font-weight: normal;
				height:65px;
				margin:0;
				margin-top:300px;
				padding:10px 15px;
				}

				.section-heading {
				display: block;
				background-color: ${colors.black};
				color: ${colors.white};
				font-size: 8px;
				font-weight: normal;
				margin:0;
				padding:2px 15px;
				}

				a {
				color: ${colors.darkBlue};
				text-decoration: none;
				}

				.label {
				font-size: 8px;
				line-height: 10px;
				display: block;
				margin:0;
				padding:2px 15px;
				}

				.desc {
				font-size: 8px;
				line-height: 10px;
				display: block;
				margin:0;
				padding:7px 15px;
				}

				.input {
				display: block;
				margin:0;
				padding: 4px 15px;
				font-weight: normal;
				font-size: 14px;
				line-height: 16px;
				background-color: ${colors.sand};
				}

				.checkbox {
				display: inline-block;
				margin:0;
				margin-right:10px;
				height: 18px;
				width: 18px;
				line-height: 18px;
				text-align: center;
				font-Weight: bold;
				background-color: ${colors.sand};
				}

				.sign-here {
				display: block;
				margin:0;
				margin-bottom:10px;
				border-bottom:solid 1px ${colors.black};
				height: 40px;
				}

			</style>
		</head>`;

		// Define header and footer templates
		const headerTemplate = `
			<div class="flex-grid" style="padding-top:20px;">
				<div class="col-half" style="width:25%;">
					<img src="${logo}">
				</div>
				<div class="col-half" style="width:75%; text-align:right;">${packageTitle}</div>
			</div>
		`;
  
	  	const footerTemplate = `
			<div class="flex-grid">
				<div class="col-half">
					${footerText}
				</div>
			</div>
		`;

		const htmlPage1 = `
		<div class="page-wrap page-break">
		<div class="flex-grid">
			<div class="col-half">
			<img src="${logo}">
			</div>
			<div class="col-half"></div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<h1 class="title-strip">${packageTitle}</h1>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<h2 class="title-sub">${subTitle}</h2>
			</div>
		</div>
		<div class="flex-grid"></div>
		</div>`;

		const htmlPage2 = `
		${headerTemplate}
		<div class="page-wrap page-break">
		<div class="flex-grid">
			<div class="col-full">
			<h4 class="section-heading">Customer Details</h4>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-half">
			<p class="label">CUSTOMER NAME</p>
			<p class="input">${data.firstName} ${data.lastName}</p>
			</div>
			<div class="col-half">
			<p class="label">ID NUMBER</p>
			<p class="input">${data.idNumber}</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<p class="label">${brandName} ACCOUNT NUMBER (IF APPLICABLE)</p>
			<p class="input">${data.api_return ? data.api_return.master_account_id_str_ignore : "&nbsp;"}</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<h4 class="section-heading">Customer Contact</h4>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<p class="label">CONTACT NAME</p>
			<p class="input">${data.firstName} ${data.lastName}</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-half">
			<p class="label">TEL</p>
			<p class="input">${data.daytimeContactNumber}</p>
			</div>
			<div class="col-half">
			<p class="label">CELL</p>
			<p class="input">${data.cellphone}</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<p class="label">E-MAIL</p>
			<p class="input">${data.email}</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<h4 class="section-heading">Install Details</h4>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-half">
			<p class="label">BUILDING NAME</p>
			<p class="input">${data.addressCode || "&nbsp;"}</p>
			</div>
			<div class="col-half">
			<p class="label">UNIT NUMBER (IF APPLICABLE)</p>
			<p class="input">${data.physical_unit_number || "&nbsp;"}</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-half">
			<p class="label">STREET</p>
			<p class="input">${data.physical_line_1}, ${data.physical_line_2}</p>
			</div>
			<div class="col-half">
			<p class="label">CITY</p>
			<p class="input">${data.physical_city}</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-half">
			<p class="label">POSTAL CODE</p>
			<p class="input">${data.physical_code}</p>
			</di${footerTemplate}v>
			<div class="col-half">
			<p class="label">GPS COORDINATES</p>
			<p class="input">${JSON.stringify(data.position) || "&nbsp;"}</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-half">
			<p class="label">BUILDING OWNER / BODY CORPORATE</p>
			<p class="input">&nbsp;</p>
			</div>
			<div class="col-half">
			<p class="label">BUILDING REPRESENTATIVE</p>
			<p class="input">&nbsp;</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-half">
			<p class="label">TEL</p>
			<p class="input">&nbsp;</p>
			</div>
			<div class="col-half">
			<p class="label">CELL</p>
			<p class="input">&nbsp;</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<p class="label">E-MAIL</p>
			<p class="input">&nbsp;</p>
			</div>
		</div>
		<div class="flex-grid"></div>
		</div>`;

		const htmlPage3 = `
		${headerTemplate}
		<div class="page-wrap">
		<div class="flex-grid">
			<div class="col-full">
			<h4 class="section-heading">Product Details</h4>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<p class="desc border">
			<strong>Infrastructure Provider</strong>: ${data.product_details.provider_name}<br>
			<strong>Product Name</strong>: ${data.product_details.name}<br>
			<strong>Product Monthly Price</strong>: ${data.product_details.monthly_price}<br>
			<strong>Product Once Off Fees</strong>: ${data.product_details.once_off_fees}<br>
			<strong>Installation/Migration Information</strong>: ${data.product_details.install_type == "data-only" ? "Data Only Package" : (data.product_details.install_type == "new" ? "New Installation" : "Activate Existing Installation")}
			</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<p class="desc">* DOWNLOAD / UPLOAD SPEED FIXED, SUBJECT TO AUP.</p>
			</div>
		</div>
		${brandName !== "FTTA" ? `<div class="flex-grid">
			<div class="col-full">
			<p class="desc">* IN THE EVENT OF YOU HAVING RECEIVED A FREE INSTALLATION AND/OR ACTIVATION AS PART OF YOUR PACKAGE,
			SHOULD YOU TERMINATE THE CONTRACT WITHIN 12 MONTHS YOU WILL BE REQUIRED TO PAY THE FULL PRICE OF THE INSTALLATION AND/OR ACTIVATION.</p>
			</div>
		</div>` : ""}
		<div class="flex-grid">
			<div class="col-full">
			<p class="desc">ROUTER IS PROVIDED WITH THE SERVICE. WIFI SPEED AND RANGE DEPENDENT ON ENVIRONMENT AND EQUIPMENT.</p>
			<p class="desc">PLEASE NOTE: THE ROUTER AND ALL INSTALLED EQUIPMENT REMAINS THE PROPERTY OF ${brandName} AND WILL BE RECOVERED UPON TERMINATION OF SERVICE.
			MONTH TO MONTH - 1 CALENDAR MONTH’S NOTICE REQUIRED.</p>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<h4 class="section-heading">SIGNATURE</h4>
			</div>
		</div>
		<div class="flex-grid">
			<div class="col-full">
			<p class="label">
				<span class="checkbox">x</span>
				I HEREBY AGREE TO ${brandName}’S TERMS AND CONDITIONS OF SERVICES AS AMENDED FROM TIME TO TIME <a href="${termsUrl}">${termsUrl.toUpperCase()}</a>
			</p>
			</div>
		</div>
		${aupUrl ? `<div class="flex-grid">
			<div class="col-full">
			<p class="label">
				<span class="checkbox">x</span>
				I HAVE READ, UNDERSTOOD AND ACCEPT THE ACCEPTABLE USE POLICY (AUP) <a href="${aupUrl}">${aupUrl.toUpperCase()}</a>
			</p>
			</div>
		</div>` : ""}
		<div class="flex-grid"></div>

		</div>
		${footerTemplate}`;

		return await createPdf();

		async function createPdf() {
			const browser = await puppeteer.launch({
				args: ['--no-sandbox', '--disable-setuid-sandbox', '--allow-file-access-from-files']
			});
			try {
				const page = await browser.newPage();

				// Set the content of the page
				await page.setContent(`<html>${htmlHead}<body>${htmlPage1}${htmlPage2}${htmlPage3}</body></html>`, {
					waitUntil: 'networkidle0'
				});

				// Generate PDF
				const pdf = await page.pdf({
					format: 'A4',
					printBackground: true,
					displayHeaderFooter: false,
					margin: {
						top: '40mm',
						bottom: '28mm',
						left: '10mm',
						right: '10mm'
					}
				});

				// Save PDF to file
				const fullPath = `${savePath}${orderTitle}.pdf`;
				await fs.writeFile(fullPath, pdf);

				// Convert to base64
				const buffer = Buffer.from(pdf);
				const pdfInBase64 = buffer.toString('base64');

				return {
					title: `${orderTitle}.pdf`,
					filename: path.resolve(fullPath),
					base64: pdfInBase64
				};
			} finally {
				await browser.close();
			}
		}
	},
};
