"use strict";

const moment = require("moment");
const rp = require("request-promise");
const telegram = require("../../../../utils/telegramSend");
const flatten = require("flat");

const FEASIBILITY_BASE_URL = strapi.config.feasibility.baseUrl;
const CONTENT_TYPE_CONTENT_EMAIL = "api::contact-email.contact-email";

const randStr = (length) => {
  let result = "";
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0*********";

  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
};


function getCreateZoneJSON(fields, zoneCode) {
    const res = {
      buildId: "Default", // leave
      publishType: "Open", // leave
      zoneStatus: "Active", // leave
      zoneType: `${fields.provider_name} Customer`,
      zoneName: zoneCode,
      zoneCode: zoneCode,
      infrastructureProvider: fields.provider_name,
      latitude: fields.position.lat,
      longitude: fields.position.lng,
      addressStreet: fields.address.route || fields.address.street_name,
      addressCity: fields.address.locality,
      addressPostcode: fields.address.postal_code,
      addressNumber: fields.address.street_number,
      addressSuburb: fields.address.political,
      addressProvince: fields.province,

      landlordType: "Owner", // leave
      personName: `${fields.user.firstName} ${fields.user.lastName}`,
      telNum: fields.user.daytimeContactNumber,
      cellNum: fields.user.cellphone,
      email: fields.user.email,
      generalContactEmail: fields.user.email,
      generalContactType: "Other", // leave
      generalContactCompany: "Person", // leave
      generalContactTelNum: fields.user.daytimeContactNumber,
      generalContactCellNum: fields.user.cellphone,
      rtTicketId: "888888", // leave
      plannedLiveDate: moment().format("DD/MM/YYYY"), // curent date "27/10/2020"
    };

    return res;
  }

  async function createMasterAccount(fields, brand) {
    console.log("createMasterAccount fields", fields);
    // WC, EC, NC, NW, FS, KZN, GP, LP, MP
    // wcp ecp ncp nwp fst kzn gtn lmp mpl

    const availableProvinceIds = {
      WC: "9",
      EC: "1",
      NC: "7",
      NW: "8",
      FS: "2",
      KZN: "4",
      GP: "3",
      LP: "5",
      MP: "6",
    };

    const province_id = availableProvinceIds[fields.physical_province];
    const idRegex = /^[\d]{13}$/;
    const passportRegex = /^(?!^0+$)[a-zA-Z0-9]{3,20}$/;

    const idNumberTypeId = idRegex.test(fields.idNumber)? "1" :
      passportRegex.test(fields.idNumber)? "3" : "2";

    let accountManagerId = null;
    let agentId = fields.agentId;
    if (brand === "Gigalight") {
      const GIGALIGHT_ACCOUNT_MANAGER_ID = 252;
      accountManagerId = GIGALIGHT_ACCOUNT_MANAGER_ID;
      agentId = agentId || "GIGALIGHTFIBRE";
    } else if (brand === "FTTA") {
      const FTTA_ACCOUNT_MANAGER_ID = 237;
      accountManagerId = FTTA_ACCOUNT_MANAGER_ID;
      agentId = agentId || "FTTA";
    }

    const physicalLine1 = [fields.physical_unit_number, fields.physical_line_1].filter(x=>x).join(", ");
    const newCustomer = {
      masterAccountData: {
        ...agentId && {dealerCode: agentId}, // add dealerCode field if agentId is specified
        ...accountManagerId && {accountManagerId},  // add needed account manager ID for a brand
        brand: brand,
        customerName: (idNumberTypeId === "1" || idNumberTypeId === "3") ?
          (fields.firstName + " " + fields.lastName) : fields.companyName,
        accountUseId: (idNumberTypeId === "1" || idNumberTypeId === "3") ? "1" : "2", // 1-Private, 2-Business
        accountTypeId: "1", // 1-Master Account, 2-Group Manager
        customerClassId: "1", // 1-Customer, 10-Platinum Customer, 100-Reseller
        idNumber: fields.idNumber,
        idNumberTypeId, // 1-ID Number, 2-Company Number, 3-Passport
        phoneNumber: fields.daytimeContactNumber,
        emailAddress: fields.email,
        statusId: "1", // Taken from accountstatus select option?
      },
      addressData: {
        physicalCity: fields.physical_city,
        physicalCode: fields.physical_code,
        // physicalCountryId: "1", // Taken from country select option?  NOT REQUIRED
        physicalLine1: physicalLine1,
        physicalline2: fields.physical_line_2,
        physicalSuburb: fields.physical_suburb,
        physicalUnitNumber: fields.physical_unit_number,
        physicalProvinceId: province_id,
        postalCode: fields.billingAddressSameAsInstallation
          ? fields.physical_code
          : fields.postalCode,
        // Not sure about this postal situation, these dont exist in the postman examples
        postalCity: "",
        // postalCountryId: "1", // Taken from country select option?  NOT REQUIRED
        postalLine1: fields.billingAddressSameAsInstallation
          ? fields.physical_line_1
          : fields.streetAddress,
        postalLine2: "",
        postalSuburb: fields.billingAddressSameAsInstallation
          ? fields.physical_suburb
          : fields.suburb,
        postalProvinceId: province_id,
      },
      paymentData: {
        // paymentTypeId: 2, // 1-Credit, 2-Debit, 3-Cash
        // accountNumber: fields.accountNumber, // Only if debit order
        // branchNumber: fields.branchCode, // Only if debit order
        // accountTypeId: 1, // Only if debit: 1-Cheque, 2-Savings, 3-Transmission
        // bankId: parseInt(fields.bank), // Only if debit
        // collectionDay: 27, // Only if debit order , day of the month to run the debit order
        // accountHolder: fields.firstName + " " + fields.lastName, // Only if debit order
        // easyMasterId: "S4ZAIDBEST2", //Only when setting payment account ONLY (set_master_payment)
        // cashSelected: "on", // NOT REQUIRED
        // cardNumber: *********, // Only if credit card
        // cardCollectionDate: 25, // Only if credit card
      },
      personData: {
        titleId: fields.titleId, // 1-Mr, 2-Mrs, 99-Empty
        contactTypeId: "1", // Taken from personcontacttypes select option
        firstName: fields.firstName,
        lastName: fields.lastName,
        cellNumber: fields.cellphone,
        emailAddress: fields.email,
        username: fields.username, // Portal username, can be randomly generated
        password: randStr(8), // Portal password, can be reandomly generated
      },
    };
    console.log("Create customer newCustomer details:", newCustomer);

    let createMasterResponse = null;

    try {
      const post = await rp({
        method: "POST",
        url: `${FEASIBILITY_BASE_URL}api/masteraccount?api=create_master`,
        headers: {
          authKey: strapi.config.feasibility.cybersmartAPIKey,
        },
        form: {
          json: JSON.stringify(newCustomer),
        },
      });

      createMasterResponse = JSON.parse(post);
      console.log("Create customer response:", createMasterResponse);

    } catch (e) {
      console.error("Create customer failed: ", e.message);
      throw {
        message: "Create customer failed"
      };
    }

    if (createMasterResponse.api_return.status !== "success") {
      // send mail if id number error (fake pass)
      const errors = createMasterResponse.api_return.errors;
      console.error("Create customer failed: ", errors);
      throw {
        errors,
        message: "Create customer failed"
      };
    }

    return createMasterResponse;
  }

  async function createInvite(fields, brand) {
    const datetime_format = "DD/MM/YYYY HH:mm";
    const start_date_time = (new moment()).format(datetime_format);
    const end_date_time = (new moment()).add(7, "days").format(datetime_format);
    let zone = null;
    const id_number = fields.idNumber;

    if (brand === "FTTA") {
      zone = strapi.config.custom.ftta_info_zone;
    } else { // Cybersmart
      zone = strapi.config.custom.cybersmart_info_zone || 1;
    }

    const newInvite = {
      cell_phone: fields.cellphone,
      zone,
      start_date_time,
      end_date_time,
      first_name: fields.firstName,
      last_name: fields.lastName,
      id_number
    };

    console.log("Pre create invite details:", newInvite);

    const url = process.env.CREATE_INVITE_URL;
    const api_key = process.env.INVITE_API_KEY || "";

    let createInviteResponse = null;
    try {
      const post = await rp({
        method: "POST",
        url,
        headers: {
          authorization: api_key,
        },
        form: newInvite,
      });

      createInviteResponse = JSON.parse(post);
      console.log("Create invite response:", createInviteResponse);
    } catch (e) {
      console.error("Create invite error: ", e.message);
      throw {
        message: `Create invite failed with ${e.message}`
      };
    }

    if (createInviteResponse.api_return.status !== "success") {
      console.error("Create invite error: ", createInviteResponse.api_return.message);
      throw {
        message: `Create invite failed. Status ${createInviteResponse.api_return.status}`
      };
    }

    return createInviteResponse;
  }

  async function createOrder(fields, masterAccountId) {
    const newOrder = {
      masteraccount: masterAccountId,
      promocodeproductid: parseInt(fields.productIds.promocodeproductid),
      productid: parseInt(fields.productIds.productid),
      zonecode: fields.addressCode,
      addressid: parseInt(fields.addressId),
      promocodeid: parseInt(fields.productIds.promocodeid),
      salesType: "New",
    };

    console.log("Pre Create order details:", newOrder);

    let createOrderResponse = null;

    try {
      const post = await rp({
        method: "POST",
        url: `${FEASIBILITY_BASE_URL}api/createorder`,
        headers: {
          authKey: strapi.config.feasibility.onlineOrderPlacementAPIKey,
        },
        form: newOrder,
      });

      createOrderResponse = JSON.parse(post);
      console.log("Create order Response:", createOrderResponse);
    } catch (e) {
      console.error("Create order error: ", e.message);
      throw {
        message: "Create order failed"
      };
    }

    if (createOrderResponse.status !== "successful") {
      const errorMessage = createOrderResponse.errorMessage ||
        createOrderResponse.api_return.errorMessage;
      console.error("Create order error: ", errorMessage);

      throw {
        message: "Create order failed",
        errors: [errorMessage],
      };
    }

    return createOrderResponse;
  }

  async function sendUpgradeRequestEmail(addressString, userInput, brand) {
    let tempJson = {addressString, ...userInput.productIds, ...userInput.product_details};
    tempJson = {... tempJson, ...userInput, ...userInput.product_details };
    let emailWasSent = false;
    try {
      let customerName = userInput.firstName + " " + userInput.lastName;
      let productName = userInput.product_details.name;
      let zoneName = userInput.addressCode;
      let subjComponents = [`Upgrade/Downgrade request (${addressString})`, customerName, productName, zoneName];

      brand = sendBrandForEmail(userInput, brand);

      let emailParams = {
        ...strapi.config.email.upgradeRequestRecipient,
        ...{to: getEmailByBrand(brand), from: getFromEmailByBrand(brand)},
        subject: subjComponents.filter(x=>x).join(" | "),
        html: generateKeyValueHtmlList("Upgrade/Downgrade request", tempJson),
      };

      const emailSent = await strapi.plugins["email"].services.email.send(emailParams);
      emailWasSent = !!emailSent.accepted.length;
      console.log("Upgrade request. Email sent", emailSent);
      if (brand.toLowerCase().includes("ftta")) {
        const botToken = strapi.config.telegram.ordersBot.token;
        const chatId = strapi.config.telegram.ordersBot.chatId;
        const tempMessage = prepareMessageForTelegram(generateKeyValueHtmlList("Upgrade/Downgrade request", tempJson));
        const telegramSent = await telegram.send(chatId, tempMessage, botToken);
        console.log("FTTA Telegram orders sent: ", telegramSent);
      }
    } catch (e) {
      console.log("Upgrade request. Error. Email not sent", e, JSON.stringify(tempJson));
    }
    return emailWasSent;
  }

function filterMatchingSubAccountsBasedOnAddress(subAccounts, addressString) {
    if (!subAccounts.length) {
      throw new Error("Empty list of sub-accounts. This should not happen here");
    }
    return subAccounts.find(sub => sub.services && sub.services.match(addressString));
  }

  async function findCustomerByAddress(addressString) {
    // FindAccounts (serviceAttribute=%addressString%)
    let accounts = null;
    try {
      accounts = await rp({
        method: "POST",
        url: `${FEASIBILITY_BASE_URL}rest/api/webapp/v2/search-api/masteraccountsearch.FindAccounts`,
        headers: {
          "Authorization": strapi.config.feasibility.searchApiAuthKey,
          "Content-Type": "application/json"
        },
        json: {serviceAttribute: `%${addressString}%`}
      });
    } catch (e) {
      console.log("findCustomerByAddress error", e);
      accounts = {status: "fail", error: e};
    }

    console.log("findCustomerByAddress response", accounts);

    if (accounts.result && accounts.result.length > 1) {
      console.log(`WARN: multiple accounts found for serviceAttribute ${addressString}`);
    }
    return accounts;
  }

  async function findCustomerByDealerCode(dealerCode) {
    // FindAccounts (dealerCode=$dealerCode)
    let accounts = null;
    try {
      accounts = await rp({
        method: "POST",
        url: `${FEASIBILITY_BASE_URL}rest/api/webapp/v2/search-api/masteraccountsearch.FindAccounts`,
        headers: {
          "Authorization": strapi.config.feasibility.searchApiAuthKey,
          "Content-Type": "application/json"
        },
        json: {dealerCode: dealerCode}
      });
    } catch (e) {
      console.log("findCustomerByDealerCode error", e);
      accounts = {status: "fail", error: e};
    }

    console.log("findCustomerByDealerCode response", accounts);

    if (accounts.result && accounts.result.length > 1) {
      console.log(`WARN: multiple accounts found for dealerCode ${dealerCode}`);
    }
    return accounts;
  }

  async function notifyAgent(dealerCode, fields) {
    let results = await findCustomerByDealerCode(dealerCode);
    if (!results.success || !results.result[0] || !results.result[0].accountId) {
      // TODO: notify developers could not find agentId by this dealer code
      console.log("notifyAgent master account Id not found for this dealerCode");
      return;
    }
    let account = results.result[0];
    let agentId = account.accountId;
    let contacts = await getContacts(agentId);
    if (!contacts.success) {
      fields.agentId = null;
      // Send error message
      console.log("createcustomer contacts not found, agentId=", agentId);
      await sendAgentError(`Can't find agent masterAccountId, agentId="${agentId},\n` +
        `errors=${JSON.stringify(contacts.errors)}"`);
    } else {
      let primaryContact = findPrimaryContact(contacts.result);
      if (primaryContact) {
        let cell = primaryContact.cellPhoneNumber;
        let name = primaryContact.contactName;
        console.log("createcustomer primaryContact=", primaryContact);
        // Send order initiated message
        const message = `Order initiated for agentId=${agentId}\n`+
          `cell=${cell}\nagent name=${name}\norder details: ${JSON.stringify(fields)}`;
        await sendAgentSale(message);
        // Send similar message to the agent
        await sendNotificationToAgent(cell, message);
      } else {
        // Send can't find cell for agent id
        await sendAgentError(`Can't find cell for agentId="${agentId}"`);
      }
    }
  }

function findPrimaryContact(contacts) {
    if (contacts.length) {
      let primaryContact = contacts.find(contact =>
        contact.contactLevel === "Primary" && contact.cellPhoneNumber && contact.contactName);
      return primaryContact;
    }
    return null;
  }

  async function getContacts(solidAccountId) {
    // GetContacts (solidAccountId)
    let contacts = null;
    try {
      contacts = await rp({
        method: "POST",
        url: `${FEASIBILITY_BASE_URL}rest/api/webapp/v2/search-api/masteraccountsearch.GetContacts`,
        headers: {
          "Authorization": strapi.config.feasibility.searchApiAuthKey,
          "Content-Type": "application/json"
        },
        json: {solidAccountId}
      });
    } catch(e) {
      console.log("getContacts error", e);
      contacts = {status: "fail", error: e};
    }
    console.log("getContacts response", contacts);
    return contacts;
  }

  async function validateBankData(bankData) {
    const soap = require("strong-soap").soap;
    var url = "https://ws.netcash.co.za/NIWS/niws_validation.svc?wsdl";

    return new Promise((resolve, reject) => {
      soap.createClient(url, {}, (err, client) => {
        if (err) {
          return reject(err);
        }
        client.ValidateBankAccount(bankData, function (err, result) {
          if (err) {
            return reject(err);
          }
          return resolve(result);
        });
      });
    });
  }

function getErrorMessage(error, path) {
  return tryParseJson(error.error);
  }

function tryParseJson(obj) {
    try {
      return JSON.parse(obj);
    } catch (e) {
      return obj;
    }
  }

  async function sendAgentError(message) {
    return await telegram.send(strapi.config.telegram.agentBot.chatId, message);
  }

  async function sendAgentSale(message) {
    return await telegram.send(strapi.config.telegram.salesBot.chatId, message);
  }

  async function sendNotificationToAgent(cell, message) {
    // TODO: move this to Communication Server (chatbot)
    if (cell.startsWith("0")) {
      cell = "+27" + cell.substring(1);
    }
    console.log("sendNotificationToAgent sending cell, message: ", cell, message);
    try {
      const CHATBOT_URL = process.env.CHATBOT_URL;
      const CHATBOT_API_KEY = process.env.CHATBOT_API_KEY;
      let result = await rp({
        method: "POST",
        url: CHATBOT_URL,
        headers: {
          "Authorization": CHATBOT_API_KEY
        },
        form: {cell: cell, smsMessage: message, multipleSms: true, telegramMessage: message}
      });
      console.log("sendNotificationToAgent result", result);
    } catch (error) {
      console.log("sendNotificationToAgent error", error.toString(), error.stack);
    }
  }

  async function sendMail (params) {
    console.log("Sending mail with subject = ", params.subject);
    const emailSent = await strapi.plugins["email"].services.email.send({...params});
    return emailSent;
  }

  async function findSubAccounts(solidAccountId) {
    // GetSubAccounts (serviceAttribute=%addressString%)
    let accounts = null;
    try {
      accounts = await rp({
        method: "POST",
        url: `${FEASIBILITY_BASE_URL}rest/api/webapp/v2/search-api/masteraccountsearch.GetSubAccounts`,
        headers: {
          "Authorization": strapi.config.feasibility.searchApiAuthKey,
          "Content-Type": "application/json"
        },
        json: {solidAccountId: solidAccountId}
      });
    } catch (e) {
      console.log("findSubAccounts error", e);
      accounts = {status: "fail", error: e};
    }

    console.log("findSubAccounts response", accounts);

    // if (accounts.result && accounts.result.length > 1) {
    //   console.log(`WARN: multiple accounts found for solidAccountId ${solidAccountId}`);
    //   accounts = null;
    //   // TODO: what to do here?
    // }
    return accounts;
  }

function getEmailByBrand(brand) {
    return strapi.config.email.emailByBrand[brand.toLowerCase()] || strapi.config.email.emailByBrand["other"];
  }

function getFromEmailByBrand(brand) {
    return  strapi.config.email.fromEmailByBrand[brand.toLowerCase()] ||  strapi.config.email.fromEmailByBrand["other"];
  }

function getBrand(ctx) {
    const MAP = process.env.DOMAIN_TO_BRAND_ID_MAP;
    let brandMap = null;
    let brand = null;
    try {
      brandMap = JSON.parse(MAP);
      console.log(`brandMap is parsed "${MAP}"`);
    } catch (e) {
      console.error(`Error parsing DOMAIN_TO_BRAND_ID_MAP as JSON. value="${MAP}"`);
    }
    const origin = ctx.header.origin;
    brand = brandMap && brandMap[origin];
    if (brand === undefined || brand === "" || brand === null) {
      brand = "Cybersmart";
    }
    console.log(`brandId is "${brand}"`);
    return brand;
}

async function sendMailToSupport(subject, fields, errors, brand) {
    let html = "";
    html += generateKeyValueHtmlList("Order Errors", errors);
    html += generateKeyValueHtmlList("Customer information", fields);
    console.log("Sending mail with subject = ", subject);

    console.log("Customer information", fields);

    brand = sendBrandForEmail(fields, brand);

    const emailSent = await strapi.plugins["email"].services.email.send({
      ...strapi.config.email.failureReportRecipient,
      ...{to: getEmailByBrand(brand), from: getFromEmailByBrand(brand)},
      subject,
      html: html,
    });

    if (brand.toLowerCase().includes("ftta")) {
      const botToken = strapi.config.telegram.ordersBot.token;
      const chatId = strapi.config.telegram.ordersBot.chatId;
      const tempMessage = subject + prepareMessageForTelegram(html);
      const telegramSent = await telegram.send(chatId, tempMessage, botToken);
      console.log("FTTA Telegram failure sent: ", telegramSent);
    }

    console.log("Email sent: ", emailSent);

    return emailSent;
  }

function generateKeyValueHtmlList(title, object, keysToStringify = ["productIds", "product_details", "position",
    "user.productIds", "user.product_details", "user.position"]) {
    let html = "";
    html += `<p><strong>${title}:</strong></p>`;

    for (const [key, val] of Object.entries(object)) {
      html += `<p><strong>${key}: </strong>${keysToStringify.includes(key) ? JSON.stringify(val) : val}</p>`;
    }

    return html;
  }

function prepareMessageForTelegram(message) {
    return message.replace(/(<([<p>]+)>)/gi, "<pre>").replace(/(<([</p>]+)>)/gi, "</pre>")
  }

function sendBrandForEmail(fields, brand) {
    let promoCodeId = fields.hasOwnProperty("user.productIds") ?
      fields["user.productIds"].promocodeid : fields["productIds"].promocodeid;
    let providerName = fields.hasOwnProperty("user.productIds") ?
      fields["user.product_details"].provider_name : fields["product_details"].provider_name;

    if (promoCodeId.includes("FTTB ON NET") && providerName.includes("Cybersmart")) {
      brand = "cybersmart-fttb";
    } else if (providerName.includes("Openserve DSL")) {
      brand = "openserve-dsl";
    }

  return brand;
}
async function createZone(body) {
  const FEASIBILITY_CREATE_ZONE_TOKEN = strapi.config.feasibility.createZoneRecordAPIKey;
  const options = {
    method: "POST",
    url: `${FEASIBILITY_BASE_URL}fibre/nodeinfo/create_zone_record`,
    headers: {
      authKey: FEASIBILITY_CREATE_ZONE_TOKEN,
    },
    form: {
      json: JSON.stringify(body),
    },
  }
  const response = await rp(options);
  return JSON.parse(response);
}
async function sendOrderEmail(fields, brand, orderNumber = null, masterAccountId = null, pdf = null) {
  try {
    const brandLowercase = this.sendBrandForEmail(fields, brand);
    const queryString = orderNumber ? `successful-order-${brandLowercase}` : `unsuccessful-order-${brandLowercase}`;
    const { results: emails } = await strapi.service(CONTENT_TYPE_CONTENT_EMAIL).find({
      filters: {
        query: queryString
      }
    });

    for (const email of emails) {
      if (email) {
        const html = email["messageContent"].replace("[orderNumber]", orderNumber);
        const zoneName = fields.addressCode;
        const fullName = `${fields.firstName} ${fields.lastName}`;
        const prodName = fields.product_details.name;
        const address = [fields.building_name, fields.physical_line_1, fields.physical_line_2,
          fields.physical_suburb, fields.physical_city].filter(x=>x).join(", ");
        const coords = `(${fields.position.lat}, ${fields.position.lng})`;
        const isPreOrder = fields.preOrder ? "- Pre-Order" : "";

        const subject = `Online Order Form ${isPreOrder} | ${orderNumber ? orderNumber + " |" : ""} ${masterAccountId ? masterAccountId + " |" : ""}
         ${fullName} | ${prodName} | ${address} | ${zoneName ? zoneName + " |" : ""} ${coords}`;

        console.log('EMAIL SUBJECT:::::::::::', {
          subject: subject,
          emailTo: fields.email
        });
        const emailParams = {
          to: fields.email,
          cc: this.getEmailByBrand(brandLowercase),
          from: this.getFromEmailByBrand(brandLowercase),
          subject: subject,
          html: html,
        };
        if (pdf) {
          emailParams.attachments = [
            {
              filename: pdf.title,
              path: pdf.filename,
            }
          ]
        }
        const emailSent = await strapi.plugins["email"].services.email.send(emailParams);
        console.log("Email order Sent:", emailSent);
      }
    }
  } catch(e) {
    console.error("Send order email failed: ", e, fields);
  }
  return true;
}
module.exports = {
  getCreateZoneJSON,
  getBrand,
  sendMailToSupport,
  getErrorMessage,
  notifyAgent,
  createMasterAccount,
  createInvite,
  createOrder,
  sendBrandForEmail,
  generateKeyValueHtmlList,
  sendUpgradeRequestEmail,
  validateBankData,
  getFromEmailByBrand,
  getEmailByBrand,
  createZone,
  sendOrderEmail,
};
