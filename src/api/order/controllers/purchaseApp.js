"use strict";

const rp = require("request-promise");

async function findCustomerContacts(cellphoneNumber, accountId) {
  let accounts = null;
  try {
    accounts = await getAccountsWithContacts(accountId);

    if (!accounts.success) {
      accounts = [];
    } else {
      accounts = accounts.result;
    }

    const customerContacts = await findContactsByPhoneNumber(accounts, cellphoneNumber);

    let parsedContacts = null;
    if(!customerContacts) {
      return parsedContacts;
    } else {
      parsedContacts = await parseContacts(customerContacts);
    }

    return parsedContacts;
  } catch (e) {
    console.error("findCustomerContacts: ERROR", e);
  }
}

async function findContactsByPhoneNumber(accounts, cellphoneNumber){
  let searchNumber = [cellphoneNumber];

  return await accounts.find(({cellPhoneNumber, homePhoneNumber, workPhoneNumber}) =>
    searchNumber.includes(cellPhoneNumber) ||
    searchNumber.includes(homePhoneNumber) ||
    searchNumber.includes(workPhoneNumber)) || null;
}

async function parseContacts(customerContacts) {
  let contactName = null;
  if (customerContacts.contactName) {
    contactName = await parseContactName(customerContacts.contactName);
  }

  const contacts = {
    "titleId": contactName.title || "",
    "firstName": contactName.firstName || "",
    "lastName": contactName.lastName || "",
    "companyName": customerContacts.customerName || "",
    "email": customerContacts.emailAddress || "",
    "cellphone": customerContacts.cellPhoneNumber || "",
    "daytimeContactNumber": customerContacts.workPhoneNumber || customerContacts.cellPhoneNumber,
    "idNumber": customerContacts.idNumber || "",
  }

  return contacts;
}

async function parseContactName(contactName) {
  const match = contactName
    .match(/((?:Mr|Ms|Mrs|Miss|Dr|Prof).?)?\s+([\w-]+)\s+(.+)$/);
  if (match) {
    let title = match[1];
    let firstName = match[2];
    let lastName = match[3];
    return { title, firstName, lastName };
  } else {
    return null;
  }
}

async function getCookieFromAuth(phoneNumber, billingCode = null, cookie) {

  let response = null;
  try {
    response = await rp({
      method: "POST",
      url: strapi.config.feasibility.authService,
      headers: {
        "Content-Type": "application/json"
      },
      json: { "phoneNumber": phoneNumber, "billingCode": billingCode, "cookie": cookie }
    });

    return response;
  } catch (e) {
    console.error("getCookieFromAuth: ERROR", e);
  }
}

async function findPrimaryContact(billingCode) {
  let accounts = await getAccountsWithContacts(billingCode);
  let searchValue = ["Primary"];
  let primaryAcc = accounts.result.find((c) => searchValue.includes(c.contactLevel)) || null;
  return primaryAcc.cellPhoneNumber;
}

async function findPrimaryContactsInformation(billingCode) {
  let primaryContactsInformation = {
    emails: [],
    cellphones: []
  };
  const accounts = await getAccountsWithContacts(billingCode);
  if (accounts.result) {
    const primaryContacts = accounts.result.filter(account => account.contactLevel === "Primary" || account.deliverInvoice === "Gets Invoice");

    const emailFilter = primaryContacts.filter(account => account.emailAddress !== "");
    const cellPhoneFilter = primaryContacts.filter(account => account.cellPhoneNumber !== "");

    primaryContactsInformation = {emails: emailFilter.map(contact => contact.emailAddress),
      cellphones: cellPhoneFilter.map(contact => contact.cellPhoneNumber)};
  }
  return primaryContactsInformation;
}


async function getAccountsWithContacts(accountId) {
  try {
    const accounts = await rp({
      method: "POST",
      url: `${strapi.config.feasibility.purchaseApp}.GetContacts`,
      headers: {
        "Authorization": strapi.config.feasibility.searchApiAuthKeyPurchase,
        "Content-Type": "application/json"
      },
      json: { "solidAccountId": accountId }
    });

    return accounts;
  } catch (e) {
    console.error("getAccountByBillingCode: ERROR", e);
  }
}

async function checkCellphone(cellphoneNumber, brand) {
  let response = await findAccountsByPhoneMultipleWays(cellphoneNumber, brand);

  return { status: "success", result: response.length > 0};
}

async function findAccountsByPhoneMultipleWays(cellphoneNumber, brand) {
  let response = await searchAccountsByPhone(cellphoneNumber);

  let accountNeededBrands;

  if (brand) {
    accountNeededBrands = checkAccountBrand(response.result, brand);
  }

  console.log("findAccountsByPhoneMultipleWays", response)
  return await getAccountsIdAndName(brand ? accountNeededBrands : response.result);
}
function checkAccountBrand(accounts, brand){
  const accountsWithBrand =  accounts.filter((acc) => {
    return acc.brand.toLowerCase().replace("\s", "") === brand.toLowerCase().replace("\s", "");
  });

  console.log("accountsWithBrand", accountsWithBrand);

  return accountsWithBrand;
}

async function getAccountsIdAndName(accounts) {
  let accountsIdAndName = [];

  if (accounts.length < 1) {
    return accountsIdAndName;
  }

  for (let account of accounts) {
    accountsIdAndName.push({ "accountId": account.accountId, "customerName": account.customerName, "accountStatus": account.accountStatus });
  }
  return accountsIdAndName;
}

async function searchAccountsByPhone(cellphoneNumber) {
  if (cellphoneNumber.startsWith("+27")) {
    // For SA numbers search for numbers starting with "+27" or "0"
    // % is a wildcard symbol
    cellphoneNumber = cellphoneNumber.replace(/^\+27/, "%");
  } else {
    // For international numbers just omit +
    cellphoneNumber = cellphoneNumber.replace(/^\+/, "%");
  }

  return await findAccountsByPhoneNumber(cellphoneNumber);
}

async function findAccountsByPhoneNumber(cellphoneNumber) {
  try {
    const [cellPhoneResponse, telePhoneResponse] = await Promise.all([
      findAccounts({ cellphoneNumber: cellphoneNumber }),
      findAccounts({ telephoneNumber: cellphoneNumber })
    ])

    const resultsArray = [...cellPhoneResponse.result, ...telePhoneResponse.result];
    const unique = Array.from(new Set(resultsArray.map(item => item))).map(item => {
      return item;
    });
    return {
      "success": (cellPhoneResponse.success && telePhoneResponse.success),
      "result": unique,
    };
  } catch (e) {
    console.error("checkPhoneNumber catched error", e, e);
  }
}

async function findAccounts(body) {
  try {
    return await rp({
      method: "POST",
      url: `${strapi.config.feasibility.purchaseApp}.FindAccounts`,
      headers: {
        "Authorization": strapi.config.feasibility.searchApiAuthKeyPurchase,
        "Content-Type": "application/json"
      },
      json: body
    });
  } catch (e) {
    console.error("findAccounts catched error", e);
  }
}


module.exports = {
  findCustomerContacts,
  checkCellphone,
  getCookieFromAuth,
  findAccountsByPhoneMultipleWays,
  findAccounts,
  findPrimaryContact,
  findPrimaryContactsInformation
}
