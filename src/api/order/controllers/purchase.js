const purchaseApp = require("./purchaseApp");

module.exports = {
  async lookupCustomerInfo(ctx) {
    const { cellphoneNumber, accountId } = ctx.request.body;

    try {
      if (!cellphoneNumber && !accountId) {
        throw new Error("Wrong param in lookupCustomerInfo!");
      }
      let customerInfo = await purchaseApp.findCustomerContacts(cellphoneNumber, accountId);
      let response;

      if (!customerInfo) {
        response = { "status": "success", "result": false };
      } else {
        response = customerInfo;
      }
      console.log("lookupCustomerInfo response", response);
      ctx.response.body = response;
    } catch (e) {
      console.error("lookupCustomerInfo: ERROR", e.message);
      ctx.response.status = 500;
      ctx.response.body = ctx.response.message = e.error || e;
    }
  },
  async checkCellphoneExistence(ctx) {
    const { cellphoneNumber, brand } = ctx.request.body;
    try {
      if (!cellphoneNumber) {
        throw new Error("Wrong param in checkCellphoneExistence!");
      }
      ctx.response.body = await purchaseApp.checkCellphone(cellphoneNumber, brand);
    } catch (e) {
      console.error("checkCellphoneExistence: ERROR", e.message);
      ctx.response.status = 500;
      ctx.response.body = ctx.response.message = e.error || e;
    }
  },
  async findAccountsByPhone(ctx) {
    const { cellphoneNumber, cookie, brand} = ctx.request.body;
    if (!cellphoneNumber || !cookie) {
      throw new Error("findAccountsByPhone: no params")
    }
    try {
      let response = null;

      let authCookie = await purchaseApp.getCookieFromAuth(cellphoneNumber, null, cookie);

      if (!authCookie.success) {
        response = { "success": true, "cookieError": true };
      } else {
        response = await purchaseApp.findAccountsByPhoneMultipleWays(cellphoneNumber, brand);
        if (response.length > 0) {
          return response;
        } else {
          response = { "success": true, "result": false };
        }
      }

      ctx.response.body = response;
    } catch (e) {
      console.error("FindAccountsByPhone: ERROR", e.message, e.stack);
      ctx.response.status = 500;
      ctx.response.body = ctx.response.message = e.error || e;
    }
  },
  async checkBillingCodeExistence(ctx) {
    const { billingCode } = ctx.request.body;
    if (!billingCode) {
      throw new Error("checkBillingCodeExistence: no params")
    }

    const bodyForRequest = { solidAccountId: billingCode };
    try {
      let response = await purchaseApp.findAccounts(bodyForRequest);
      if (response.result.length >= 1) {
        response = { "success": true, "result": true };
      } else {
        response = { "success": true, "result": false };
      }
      ctx.response.body = response;
    } catch (e) {
      console.error("checkBillingCodeExistence: ERROR", e);
      ctx.response.status = 500;
      ctx.response.body = ctx.response.message = e.error || e;
    }
  },
  async sendCellphoneToAuth(ctx) {
    const { billingCode } = ctx.request.body;
    if (!billingCode) {
      throw new Error("sendCellphoneToAuth: no params")
    }

    try {
      let response = await purchaseApp.findPrimaryContact(billingCode);
      response = { "cellPhoneNumber": response };
      ctx.response.body = response;
    } catch (e) {
      console.error("sendCellphoneToAuth: ERROR", e);
      ctx.response.status = 500;
      ctx.response.body = ctx.response.message = e.error || e;
    }
  },
  async sendPrimaryContactsInformation(ctx) {
    const { billingCode } = ctx.request.body;
    if (!billingCode) {
      throw new Error("sendCellphoneToAuth: no params")
    }

    try {
      ctx.response.body = await purchaseApp.findPrimaryContactsInformation(billingCode);
    } catch (e) {
      console.error("sendPrimaryContactsEmails: ERROR", e);
      ctx.response.status = 500;
      ctx.response.body = ctx.response.message = e.error || e;
    }
  },
  async findAccountByBillingCode(ctx) {
    const { billingCode, cellphoneNumber, cookie } = ctx.request.body;

    if (!billingCode && !cellphoneNumber && !cookie) {
      throw new Error("Wrong parameters in findAccountByBillingCode");
    }

    try {
      let response = null;
      const bodyForRequest = { solidAccountId: billingCode };

      let authCookie = await purchaseApp.getCookieFromAuth(cellphoneNumber, billingCode, cookie);

      if (!authCookie.success) {
        response = { "success": true, "cookieError": true };
      } else {
        response = await purchaseApp.findAccounts(bodyForRequest);
        if (!response.result.length < 1) {
          response = [{ "accountId": response.result[0].accountId, "customerName": response.result[0].customerName }];
        } else {
          response = { "success": true, "result": false };
        }
      }

      ctx.response.body = response;
    } catch (e) {
      console.error("findAccountByBillingCode: ERROR", e);
      ctx.response.status = 500;
      ctx.response.body = ctx.response.message = e.error || e;
    }
  }
}
