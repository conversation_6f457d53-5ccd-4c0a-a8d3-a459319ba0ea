"use strict";

/**
 * A set of functions called "actions" for `order`
 */
const rp = require("request-promise");
const flatten = require("flat");
const util = require("util");

const orderPdf = require("./makeOrderPdf");
const orderApp = require("./orderApp");
const telegram = require("../../../../utils/telegramSend");
const moment = require("moment");


const DATA_FOR_UPLOAD_PDF = {
  "DEBIT": {
    docTypeId: 9,
    subTypeId: 1,
    departmentId: 2,
    labelId: 5,
    statusId: 4,
    publishTypeId: 2,
    owner: "webapi"
  },
  "APPLICATION": {
    docTypeId: 6,
    subTypeId: 3,
    departmentId: 1,
    labelId:  8,
    statusId: 4,
    publishTypeId: 2,
    owner: "webapi"
  }
};

const FEASIBILITY_BASE_URL = strapi.config.feasibility.baseUrl;
const FEASIBILITY_CREATE_ZONE_TOKEN = strapi.config.feasibility.createZoneRecordAPIKey;
const FEASIBILITY_DISTRIBUTION_NODE_KEY = strapi.config.feasibility.distributionNodeKey;
const CONTENT_TYPE_CONTENT_EMAIL = "api::contact-email.contact-email";

const ACTIVE_ACCOUNT = "Active Account";
const ALREADY_EXIST_MESSAGE = "already";

module.exports = {
  testPdf: async (ctx = ({strapi})) => {
    const fields = ctx.request.body;

    try {
      console.log("::: PDF START :::");
      const pdf = await orderPdf.makePdf(fields);
      ctx.response.body = pdf;
      console.log("::: PDF RESULT :::", pdf);
    } catch (e) {
      console.error("::: PDF Error :::", e);
      ctx.response.status = 500;
      ctx.response.body = ctx.response.message = e.error || e;
    }
  },
  createZoneRecord: async (ctx = ({strapi})) => {
    const fields = ctx.request.body;


    if(!fields.user.firstName || !fields.user.lastName){
      throw new Error("No contact info");
    }

    const openserve_dsl_substrings = ["openserve adsl", "openserve dsl"];
    const product_name = fields.user.product_details.name.toLocaleLowerCase();
    if (openserve_dsl_substrings.filter(str => product_name.includes(str)).length) {
      fields.provider_name = "Openserve DSL";
      console.log("Zone Create provider name changed to: Openserve DSL");
    }
    const unit = fields.user.physical_unit_number ? "Unit " + fields.user.physical_unit_number : "";
    const address = [unit, fields.address.street_number || "", fields.address.route || fields.address.street_name || "",
      fields.address.political || "", fields.address.locality || "", fields.address.postal_code || ""].filter(el => el).join(", ");

    let zoneCode = `${fields.provider_name} ${fields.address.street_number} ${
      fields.address.route || fields.address.street_name
    } ${fields.address.political}`.replace(/\s/g, "");
    console.log("Zone create zoneCode:", zoneCode);
    let tempJson = orderApp.getCreateZoneJSON(fields, zoneCode);

    try {
      const newZone = await orderApp.createZone(tempJson)
      console.log("Zone created response:", newZone);

      if (newZone.status !== "Success") {
        const { distributionNodeCode, longitude } = newZone.errors || {};
        const zoneAlreadyExists = distributionNodeCode && distributionNodeCode.includes(ALREADY_EXIST_MESSAGE);
        const zoneGpsAlreadyExists = longitude && longitude.includes(ALREADY_EXIST_MESSAGE);

        if (zoneAlreadyExists) {
          ctx.response.status = 200;
          ctx.response.body = {
            status: "Success",
            zoneExisted: true,
            zoneCode: zoneCode
          }
          strapi.log.info("Create Zone error:: Zone already exist. Send zoneCode ", zoneCode);
          return;
        } else if (zoneGpsAlreadyExists) {
          tempJson.latitude += 0.0000001;
          const newZone = await orderApp.createZone(tempJson);
          if (newZone.status !== "Success") {
            throw {message: "Zone create failed (after trying with + 0.1 latitude )",
              errors: newZone.errors};
          }
          newZone.zoneExistedGps = true;
          ctx.response.status = 200;
          ctx.response.body = JSON.stringify(newZone);
          strapi.log.info(`Zone created response (GPS Error): new latitude: ${tempJson.latitude}`, newZone);
          return;
        }
        throw {message: "Zone create failed",
          errors: newZone.errors};
      }
      ctx.response.body = JSON.stringify(newZone);
    } catch (e) {
      console.error("Create Zone error::", orderApp.getErrorMessage(e.message)); // TODO: change path
      const customerName = fields.user.firstName + " " + fields.user.lastName;
      const subject = e.message + `. Customer name = ${customerName}, Address = ${address}, ZoneCode = ${zoneCode}`;
      try {
        const brand = orderApp.getBrand(ctx);
        await orderApp.sendMailToSupport(subject, flatten(fields, {maxDepth: 2}), e.errors, brand);
        await orderApp.sendOrderEmail(fields.user, brand.toLowerCase());
        ctx.response.status = 200;
        ctx.response.body = {
          status: "Error",
          message: "Create zone error",
          fakePass: true
        };
      } catch (e) {
        console.error("Email of create zone failed Error::", e);
        ctx.response.status = 500;
        ctx.response.body = {
          status: "Error",
          message: "Internal server error. Please try again later",
          zoneCode: zoneCode,
        };
      }
    }
  },
  createAddress: async (ctx = ({strapi})) => {
    const fields = ctx.request.body;
    console.log("Pre Address Create Fields:", fields);

    if(!fields.orderDetails.firstName || !fields.orderDetails.lastName){
      throw new Error("No contact info");
    }
    const unit = fields.orderDetails.physical_unit_number ? "Unit " + fields.orderDetails.physical_unit_number : "";
    const address = [unit, fields.orderDetails.physical_line_1,
      fields.orderDetails.physical_suburb, fields.orderDetails.physical_city,
      fields.orderDetails.physical_code].filter(el => el).join(", ");


    let tempJson = {
      distributionNodeCode: fields.distribution_node_code,
      streetNumberAndName: fields.address_street,
      Unit: fields.address_unit,
    };
    console.log("Pre Address Create:", tempJson);
    let newAddress = null;
    try {
      const post = await rp({
        method: "POST",
        url: `${FEASIBILITY_BASE_URL}fibre/nodeunits/create`,
        headers: {
          authKey: FEASIBILITY_DISTRIBUTION_NODE_KEY,
        },
        form: {
          json: JSON.stringify(tempJson),
        },
      });

      newAddress = JSON.parse(post);
      console.log("Address Create Response:", newAddress);

      if (newAddress.status !== "success") {
        if (newAddress.error && newAddress.error.includes(ALREADY_EXIST_MESSAGE)) {
          const response = await rp({
            method: "POST",
            url: `${FEASIBILITY_BASE_URL}fibre/nodeunits`,
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              authKey: FEASIBILITY_DISTRIBUTION_NODE_KEY,
            },
            form: {
              distributionNodeCode: fields.distribution_node_code,
            },
            json: true,
          });
          if (response.addresses) {
            const unit = fields.orderDetails.physical_unit_number ? "Unit " + fields.orderDetails.physical_unit_number : "";
            const address = [unit, fields.orderDetails.physical_line_1, fields.orderDetails.physical_suburb,
              fields.orderDetails.locality,fields.orderDetails.physical_province, fields.orderDetails.physical_code].filter(el => el).join(", ");
            const neededAddress = response.addresses.filter((item) => {
              return item.displayAddress === address
            })
            if (neededAddress.length) {
              ctx.response.status = 200;
              ctx.response.body = neededAddress[0];
              return;
            }
          }
        }
        throw { message: newAddress.message,  error: newAddress.error };
      }

    } catch (e) {
      try {
        const customerName = fields.orderDetails.firstName + " " + fields.orderDetails.lastName;
        const subject = `Address Create Error. Customer name = ${customerName}, Address = ${address}, Zonecode = ${fields.distribution_node_code}`;
        const errors = e.error? [e.error]: [];
        const brand = orderApp.getBrand(ctx);
        await orderApp.sendMailToSupport(subject, fields.orderDetails, errors, brand);
        await orderApp.sendOrderEmail(fields.orderDetails, brand.toLowerCase());
        ctx.response.status = 200;
        ctx.response.body = { status: "address create error", orderNumber: null, fakePass: true};
        return;
      } catch (e) {
        console.error("Email if Address Create Error::", e);
        ctx.response.status = 500;
        ctx.response.body = ctx.response.message =  "Internal server error. Please try again later";
        return;
      }
    }

    // Get Node Addresses
    try {
      const response = await rp({
        method: "POST",
        url: `${FEASIBILITY_BASE_URL}fibre/nodeunits`,
        headers: {
          authKey: FEASIBILITY_DISTRIBUTION_NODE_KEY,
        },
        form: {
          distributionNodeCode: fields.distribution_node_code,
        },
      });

      const addressResponse = JSON.parse(response);
      console.log("Single address response:", response);

      // get single address info
      const singleAddress = addressResponse.addresses.find(address => address.id === newAddress.addressId);
      console.log("Single address info:", singleAddress);

      //ctx.type = "application/json";
      ctx.response.body = JSON.stringify({
        ...newAddress,
        displayAddress: singleAddress.displayAddress,
      });
    } catch (e) {
      try {
        const subject = `Failed Order Application (Get address info error). Zonecode = ${fields.distribution_node_code}`;
        const brand = orderApp.getBrand(ctx);
        await orderApp.sendMailToSupport(subject, fields.orderDetails, [], brand);
        ctx.response.status = 200;
        ctx.response.body = { status: "address create error", orderNumber: null, fakePass: true };
      } catch (e) {
        console.error("Email if Address Create Error::", e);
        ctx.response.status = 500;
        ctx.response.body = ctx.response.message =  "Internal server error. Please try again later";
      }
    }
  },
  createCustomer: async (ctx = ({strapi})) => {
    const fields = ctx.request.body;

    if(!fields.firstName || !fields.lastName){
      throw new Error("No contact info");
    }

    const brand = orderApp.getBrand(ctx);
    const unit = fields.physical_unit_number? "Unit " + fields.physical_unit_number : "";
    const address = [unit, fields.physical_line_1, fields.physical_suburb,
      fields.physical_city, fields.physical_code].filter(el => el).join(", ");

    let createMasterResponse = null, createInviteResponse = null, createOrderResponse = null;

    try {
      let dealerCode = fields.agentId;
      if (dealerCode) {
        await orderApp.notifyAgent(dealerCode, fields);
      }

      if(!fields.accountId){
        createMasterResponse = await orderApp.createMasterAccount(fields, brand);
      //  createInviteResponse = await orderApp.createInvite(fields, brand);
        const accountId = createMasterResponse.api_return.master_account_id_str_ignore;
        createOrderResponse = await orderApp.createOrder(fields, accountId);
      } else {
        createOrderResponse = await orderApp.createOrder(fields, fields.accountId);
        createMasterResponse = {
          api_return: {
            master_account_id_str_ignore: fields.accountId
          }
        }
      }

    } catch (e) {
      console.error("Createcustomer catched error", e);
      const customerName = fields.firstName + " " + fields.lastName;
      const subject = e.message + `. Customer name = ${customerName}, Address = ${address}, ZoneCode = ${fields.addressCode}`;

      try {
        const emailSent = await orderApp.sendMailToSupport(subject, fields, e.errors || e, brand);
        await orderApp.sendOrderEmail(fields, brand.toLowerCase());
        ctx.response.status = 200; // Show "complete" on frontend if send mail succeeded
        ctx.response.body = { status: "Create customer error", orderNumber: null, fakePass: true};
        return ;
      } catch (e) {
        console.error("Email sending failed with subject = ", subject);
        ctx.response.status = 500; // Show error to user if email failed
        ctx.response.body = ctx.response.message = "Internal server error. Please try again later.";
        return ;
      }
    }

    try {
      await sendSuccessPdf(fields, createOrderResponse, createMasterResponse, brand);
      ctx.response.status = 200;
      ctx.response.body = createOrderResponse;
    } catch (e) {
      console.error("PDF Error::", e);
      ctx.response.status = 500;
      ctx.response.body = ctx.response.message = "Internal server error. Please try again later.";
    }
  },
  setPaymentMethod: async (ctx = ({strapi})) => {
    const fields = ctx.request.body;
    console.log("Bank details received fields", ctx.request.body);
      try {
        const idNumber = fields.idNumber;
        const cell = fields.cell;
        const customerInfo = await lookupCustomerInfoByIdNumber(idNumber);
        const customerName = customerInfo.customerName ? customerInfo.customerName : "unknown customer";
        let easyMasterId = customerInfo.easyMasterId;
        if (!easyMasterId) {
          await reportCouldNotSetMasterPaymentError(
            `No easyMasterId found for idNumber "${idNumber}", cell = ${cell}`, {}, fields);
          ctx.response.status = 200;
          ctx.response.body = {status: "successful"};
          return;
        }
        let paymentData = extractPaymentData(fields, easyMasterId);
        console.log("paymentData", paymentData);
        let res = await setMasterPayment(paymentData);
        if (res.api_return.status === "fail") {
          await reportCouldNotSetMasterPaymentError(
            `Failed to set master payment for ${customerName}. Cell = ${cell}`,
            res.api_return, fields);
        }
        if (fields.pdf) {
          await sendPdfToSolid(
            paymentData.paymentData.easyMasterId,
            paymentData.paymentData.easyMasterId,
            DATA_FOR_UPLOAD_PDF["DEBIT"],
            fields.pdf,
            "Debit Order Mandate - Signed"
          );
        }
        ctx.response.status = 200;
        ctx.response.body = {status: "successful"};
      } catch (e) {
        console.error("setpaymentmethod error:", e);

        //TODO: Think about email here
        ctx.response.status = 500;
        ctx.response.body = {status: "fail", message: e.toString()};
      }
      return;

      async function reportCouldNotSetMasterPaymentError(subject, apiReturn, fields) {
        try {
          let html = "";

          html += orderApp.generateKeyValueHtmlList("Set master payment error", apiReturn, ["errors"]);

          if (fields.questionnaireAnswers) {
            html += orderApp.generateKeyValueHtmlList("Questionnaire answers", fields.questionnaireAnswers, [0, 1, 2, 3, 4, 5]);
          }
          let otherFields = {...fields};
          delete otherFields["questionnaireAnswers"];
          html += orderApp.generateKeyValueHtmlList("Other fields", otherFields, []);

          console.log("Sending mail with subject = ", subject);
          const emailSent = await strapi.plugins["email"].services.email.send({
            ...strapi.config.email.failureReportRecipient, subject, html,
          });
          console.error("Failed to set master payment email sent:", emailSent);
        } catch (e) {
          console.error("Could not send debug email", JSON.stringify(apiReturn), JSON.stringify(fields), e);
        }
      }

      async function setMasterPayment(paymentObj) {
        const textResponse = await rp({
          method: "POST",
          url: `${FEASIBILITY_BASE_URL}api/masteraccount?api=set_master_payment`,
          headers: {
            authKey: strapi.config.feasibility.cybersmartAPIKey,
          },
          form: {
            json: JSON.stringify(paymentObj),
          },
        });
        console.log("setMasterPayment response", JSON.parse(textResponse));

        return JSON.parse(textResponse);
      }

      function extractPaymentData(fields, masterId) {
        // Bank details recieved fields
        // obj={ questionnaireAnswers:
        //    [ { answerId: "1", question: "Bank:", answer: "BARCLAYS BANK" },
        //      { answerId: "2", question: "Branch:", answer: "8" },
        //      { answerId: "3", question: "Branch No:", answer: "9" },
        //      { answerId: "4", question: "Account name:", answer: "10" },
        //      { answerId: "5",
        //        question: "Type of Account: (Current/Savings/etc)",
        //        answer: "Current" },
        //      { answerId: "6", question: "Account No:", answer: "12" } ]}
        const DEBIT_PAYMENT = 2;
        const answers = fields.questionnaireAnswers;
        let accountHolderName = findAnswer(3, answers);
        let branchNumber = findAnswer(2, answers);
        let accountTypeStr = findAnswer(4, answers);
        let accountType = mapTypeOfAccount(accountTypeStr);
        let bankName = findAnswer(1, answers);
        bankName = bankName.replace(/[^a-zA-Z 0-9()/]+/g, "");
        let bankId = bankNameToId(bankName);
        let accountNumber = findAnswer(5, answers);
        const COLLECTION_DAY = 27; // Always set collection date on the 27th day of a month
        return {
          paymentData: {
            paymentTypeId: DEBIT_PAYMENT,
            collectionDay: COLLECTION_DAY,
            accountHolder: accountHolderName,
            branchNumber: branchNumber,
            accountTypeId: accountType,
            bankId: bankId,
            accountNumber: accountNumber,
            easyMasterId: masterId
          }
        };
      }

      function mapTypeOfAccount(typeStr) {
        let map = {
          "current": 1,
          "savings": 2,
          "transmission": 1
        };
        let typeOfAccountInt = map[typeStr.toLowerCase()];
        return typeOfAccountInt || null;
      }

      function findAnswer(answerId, answers) {
        let answer = answers.find(answer => parseInt(answer.answerId) === answerId);
        return answer ? answer.answer : null;
      }

      function bankNameToId(bankName) {
        const bankNameToIdMap = {
          "absa (allied)": 1,
          "absa (trust bank)": 2,
          "absa (united)": 3,
          "absa (volkskas)": 4,
          "absa bank": 5,
          "old mutual bank": 6,
          "bank of athens": 7,
          "bank of namibia": 8,
          "bank of taiwan": 9,
          "bank of transkei": 10,
          "bank of windhoek": 11,
          "boland bank": 12,
          "cape of good hope": 13,
          "citibank": 14,
          "community bank": 15,
          "credit agricole in": 16,
          "fidelity bank": 17,
          "first national bank": 18,
          "french bank": 19,
          "future bank": 20,
          "habib overseas bank": 21,
          "hbz bank limited": 22,
          "investec bank ltd": 23,
          "lesotho bank": 24,
          "mercantile bank": 25,
          "nbs/ican": 26,
          "nedbank/perm": 27,
          "permanent bank": 28,
          "reserve bank": 29,
          "saambou/20twenty": 30,
          "standard bank": 31,
          "swabou": 32,
          "unibank limited": 33,
          "old mutual bank": 34,
          "boe bank": 35,
          "capitec bank": 36,
          "sa post office": 37,
          "rand merchant bank": 38,
          "abn amro bank nv": 39,
          "bidvest": 40,
          "hsbc": 41,
          "societe general jhb branch": 42,
          "african bank": 43,
          "ubank ltd": 44,
          "nedbank namibia": 45,
          "barclays bank": 46,
          "albaraka bank": 47,
          "state bank of india": 48,
          "swaziland dev and savings bank": 49,
          "olympus mobile": 50,
          "cyberbills clearing bank": 51,
          "sasfin bank limited": 52,
          "discovery bank": 53,
          "tyme bank": 54,
          "netcash": 55,
          "bank zero": 56
        };
        let bankNameLowerCase = bankName.toLowerCase();
        let bankId = bankNameToIdMap[bankNameLowerCase];
        if (!bankId) {
          throw new Error(`Could not find an ID for bank name="${bankName}"`);
        }
        return bankId;
      }

      async function lookupCustomerInfoByIdNumber(idNumber) {
        // If this check would be enabled business id"s should also be checked
        // if (!validIdNumber(idNumber)) {
        //   console.log(`idNumber "${idNumber}" is not valid`);
        //   throw new Error("idNumber is not valid");
        // }
        let accounts = null;
        try {
          accounts = await rp({
            method: "POST",
            url: `${FEASIBILITY_BASE_URL}rest/api/webapp/v2/search-api/masteraccountsearch.FindAccounts`,
            headers: {
              "Authorization": strapi.config.feasibility.searchApiAuthKey,
              "Content-Type": "application/json"
            },
            json: {idNumber}
          });
          console.log("lookupCustomerInfoByIdNumber response", accounts);

          if (accounts.result) {
            if (accounts.result.length > 1) {
              console.error(`WARN: multiple accounts found for idNumber ${idNumber}`);
              // TODO: report multiple accounts with the same idNumber to the TG chat
            }
            accounts.result = accounts.result.filter(account => account.accountStatus === ACTIVE_ACCOUNT);
          }

        } catch (e) {
          console.error("lookupCustomerInfoByIdNumber error", e);
          accounts = {status: "fail", error: e};
        }
        let easyMasterId = (accounts && accounts.result && accounts.result[0] && accounts.result[0].accountId) || undefined;
        const customerName = (accounts && accounts.result && accounts.result[0] && accounts.result[0].customerName) || undefined;

        return {easyMasterId, customerName};
      }
  },
  createContact: async (ctx = ({ strapi })) => {
    const fields = ctx.request.body;

    try {
      const response = await rp({
        method: "POST",
        url: strapi.config.feasibility.createContact.url,
        headers: {
          Authorization: strapi.config.feasibility.createContact.authKey,
        },
        body: fields,
        json: true,
      });
      ctx.response.status = 200;
      ctx.response.body = { status: "successful" };
      console.log("Create Contact successful:", JSON.stringify(response));
    } catch (error) {
      console.log("Create contact failed: ", JSON.stringify(error));
      ctx.response.status = 500;
    }
  },
  submitUpgradeDowngrade: async (ctx = ({ strapi })) => {
    const fields = ctx.request.body;

    console.log("Submit upgrade downgrade. received fields", fields);

    let addressString = [fields.physical_unit_number, fields.physical_line_1, fields.physical_suburb].filter(x => x).join(", ");
    const brand = orderApp.getBrand(ctx);
    let emailSent = await orderApp.sendUpgradeRequestEmail(addressString, fields, brand);
    ctx.response.status = 200;
    ctx.response.body = {addressString, emailSent};
  },
  validatePaymentData: async (ctx = ({ strapi })) => {
    const fields = ctx.request.body;
    console.log("Validate payment data received fields", fields);

    let answers = fields.questionnaireAnswers.answers;

    if (answers[1].answer.length !== 6) {
      const result = {type: "specific_error", questionId: 2, message: "Branch code should be 6 digits long"};
      console.log("Validation result: ", JSON.stringify(result));
      ctx.response.status = 200;
      ctx.response.body = {result};
      return;
    }

    const AccountType = (answers[3].answer == "Current" || answers[3].answer == "Transmission") ? 1 :
      answers[3].answer == "Savings" ? 2 : 0;
    var args = {
      ServiceKey: "cd4a1f5e-b917-4192-9dcb-ea885247ee15",
      AccountNumber: answers[4].answer,
      BranchCode: answers[1].answer,
      AccountType};

    let result = null;

    try {
      result = await orderApp.validateBankData(args);
    } catch (e) {
      console.log("Validation failed: ", JSON.stringify(e));
      ctx.response.status = 500;
      return;
    }

    const dict = {
      "0": {type: "success", message: "Bank account details valid"},
      "1": {type: "specific_error", questionId: 2, message: "Invalid branch code"},
      "2": {type: "specific_error", questionId: 5, message: "Account number failed check digit validation"},
      "3": {type: "specific_error", questionId: 4, message: "Invalid account type"},
      "4": {type: "common_error", message: "Input data incorrect"},
      "100": {type: "fail", message: "Authentication failed"}
    };

    const decodedResult = dict[result.ValidateBankAccountResult];

    console.log("Validation result code: ",  result.ValidateBankAccountResult);
    console.log("Validation result decoded: ", JSON.stringify(decodedResult));

    if (decodedResult.type === "fail") {
      ctx.response.status = 500;
      return;
    }

    ctx.response.status = 200;
    ctx.response.body = {result: decodedResult};
  }
};

async function sendSuccessPdf(fields, createOrderResponse, createMasterResponse, brand) {

  console.log("CREATE MASTER RESPONSE:::", createMasterResponse)

  console.log("::: PDF START :::");
  let brandLowercase = brand.toLowerCase();

  const masterAccount =  createMasterResponse.api_return.master_account_id_str_ignore;
  const orderNumber = createOrderResponse.orderNumber;

  const pdf = await orderPdf.makePdf({
    ...fields,
    ...createOrderResponse,
    ...createMasterResponse,
    brand: brandLowercase
  });
  console.log("::: PDF RESULT :::", {
    title: pdf.title,
    filename: pdf.filename
  });

  // Send success email
  try {
    await orderApp.sendOrderEmail(fields, brandLowercase, orderNumber, masterAccount, pdf);
    await sendPdfToSolid(
      masterAccount,
      orderNumber,
      DATA_FOR_UPLOAD_PDF["APPLICATION"],
      pdf.base64,
      "Application form"
    );
  } catch (e) {
    console.error("Email successful order Error::", e, util.inspect(e));
  }
}
async function sendPdfToSolid(accountId, identyNameFile, constDoc,  file, fileName) {
  try {
    const tempBody = {
      "accountId": accountId,
      "documentTypeId": constDoc.docTypeId,
      "documentSubTypeId": constDoc.subTypeId,
      "owner": constDoc.owner,
      "departmentId": constDoc.departmentId,
      "labelId": constDoc.labelId,
      "statusId": constDoc.statusId,
      "publishTypeId": constDoc.publishTypeId,
      "fileName": fileName + " - " + identyNameFile + ".pdf",
      "fileData": file
    };

    console.log("Sending PDF to solid TEMPLATE BODY: ", {
      "accountId": accountId,
      "fileName": fileName + " - " + identyNameFile + ".pdf",
    });
    const response = await rp({
      method: "POST",
      url: strapi.config.feasibility.document.url,
      auth: {
        bearer: strapi.config.feasibility.document.authKey
      },
      body: tempBody,
      json: true,
    });
    console.log("PDF sending successful:", response);
  } catch(e) {
    console.error("Sending PDF Error::", e, util.inspect(e));
  }
}
