module.exports = {
  routes: [
    {
      method: "POST",
      path: "/order/find_account_by_billing_code",
      handler: "purchase.findAccountByBillingCode",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/get_primary_cellphone",
      handler: "purchase.sendCellphoneToAuth",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/get_primary_information",
      handler: "purchase.sendPrimaryContactsInformation",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/check_billing_code",
      handler: "purchase.checkBillingCodeExistence",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/find_accounts",
      handler: "purchase.findAccountsByPhone",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/lookup_existing_customer",
      handler: "purchase.lookupCustomerInfo",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/cell_exist",
      handler: "purchase.checkCellphoneExistence",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/create_customer",
      handler: "order.createCustomer",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/set_payment_method",
      handler: "order.setPaymentMethod",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/add_address",
      handler: "order.createAddress",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/create_zone_record",
      handler: "order.createZoneRecord",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/testpdf",
      handler: "order.testPdf",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/create_contact",
      handler: "order.createContact",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/upgrade_downgrade",
      handler: "order.submitUpgradeDowngrade",
      config: {
        policies: []
      }
    },
    {
      method: "POST",
      path: "/order/validate",
      handler: "order.validatePaymentData",
      config: {
        policies: []
      }
    }
  ],
};
