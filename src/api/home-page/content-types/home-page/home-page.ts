// Interface automatically generated by schemas-to-ts

import { Seo } from '../../../../components/shared/interfaces/Seo';
import { Seo_Plain } from '../../../../components/shared/interfaces/Seo';
import { Seo_NoRelations } from '../../../../components/shared/interfaces/Seo';

export interface HomePage {
  id: number;
  attributes: {
    createdAt: Date;    updatedAt: Date;    publishedAt?: Date;    Content: any;
    seo?: Seo;
  };
}
export interface HomePage_Plain {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  Content: any;
  seo?: Seo_Plain;
}

export interface HomePage_NoRelations {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  Content: any;
  seo?: Seo_NoRelations;
}

export interface HomePage_AdminPanelLifeCycle {
  id: number;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;  Content: any;
  seo?: Seo_Plain;
}
