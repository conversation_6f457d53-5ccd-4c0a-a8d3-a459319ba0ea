// @import "vue-select/src/scss/vue-select.scss";

$our_blue : #01a5e8;

.v-select {
    margin-top: 1rem;
    margin-bottom: 1rem;
    font-weight: initial;
    min-width: $vs-dropdown-min-width;
}

.vs__selected-options {
  flex-wrap: initial;
}

.vs__selected {
  margin: 0;
  border: initial;
  border-radius: 0;
}

.vs__dropdown-toggle {
  background-color: #fafafa;
  border: initial;
  border-radius: 0;
  white-space: normal;
  padding:0.75rem;

  &:hover {
    background-color: #e1e1e1;
  }
}

.vs__dropdown-option {
  white-space: normal;
  padding: 0.75rem;

  &:hover {
    background-color: $our_blue;
  }
}

.vs__dropdown-option--highlight {
    background: $our_blue;
}
.vs__dropdown-option--disabled {

}

.vs__search::placeholder {
  color: $vs-component-placeholder-color;
  line-height: $line-height;
  font-size: $font-size;
  padding: 0 7px;
  letter-spacing: 2px;
}
