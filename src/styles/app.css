@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	
}

p {
	@apply text-lg lg:text-lg 2xl:text-base
}

@layer utilities {
	.text-h1 {
		@apply text-3xl lg:text-4xl 2xl:text-5xl
	}

	.text-h2 {
		@apply text-2xl lg:text-2xl 2xl:text-3xl
	}

	.text-h3 {
		@apply text-xl lg:text-2xl 2xl:text-2xl
	}

	.text-p {
		@apply text-lg lg:text-lg 2xl:text-base
	}

	.text-display {
		@apply text-5xl lg:text-6xl 2xl:text-7xl
	}

	.text-subtitle {
		@apply text-lg lg:text-lg 2xl:text-base
	}

	.prose p {
		@apply text-slate-500 font-light mb-6
	}
}

.material-symbols-outlined {
	font-variation-settings:
	'FILL' 0,
	'wght' 300,
	'GRAD' 0,
	'opsz' 24
}

.material-symbols-outlined.filled {
	font-variation-settings:
	'FILL' 1,
	'wght' 300,
	'GRAD' 0,
	'opsz' 24
}
