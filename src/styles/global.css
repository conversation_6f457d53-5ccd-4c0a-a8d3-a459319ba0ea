@import url('https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.15.1/cdn/themes/light.css');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
	--sl-color-primary-600: var(--sl-color-sky-50);
}

/* Optional: Set Roboto as the default font family */
body {
  @apply font-roboto;
}

.material-symbols-outlined {
	font-variation-settings:
	'FILL' 0,
	'wght' 300,
	'GRAD' 0,
	'opsz' 24
}

