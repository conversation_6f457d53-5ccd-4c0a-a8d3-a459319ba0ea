// import Vue from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import store from '@/store'
import Home from '../views/Home.vue'
import { useScroll } from '@/composables/useScroll';

const router = createRouter({
	history: createWebHistory(),
	base: import.meta.env.BASE_URL,
	routes: [
		{
			path: '/:pathMatch(.*)*',
			name: 'zone-handler',
			component: Home
		},
		{
			path: '/',
			name: 'home',
			component: Home,
			props: true,
			beforeEnter(to, from, next) {
				// console.log("home", store.state.agentId);
				store.commit('setFreshSearch');
				store.commit('setError', to.params.error);
				next();
			}
		},

		{
			path: '/a/:agentId',
			name: 'agent-affiliated-home',
			component: Home,
			props: true,
			beforeEnter(to, from, next) {
				// console.log("home2", to.params.agentId);
				store.commit('setAgentId', to.params.agentId);
				store.commit('setFreshSearch');
				store.commit('setError', to.params.error);
				next("/");  // Forward to the root (Home page)
			}
		},
		{
			path: '/location/:address/a/:agentId',
			name: 'agent-affiliated-location',
			props: true,
			component: () => import('../views/ProductSelection.vue'),
			beforeEnter(to, from, next) {
				const address = to.params.address;
				store.commit('setAgentId', to.params.agentId);
				next(`/location/${address}`);  // Forward to the /location/:address
			}
		},
		{
			path: '/location/:address/:forbusiness?',
			name: 'location',
			props: true,
			component: () => import('../views/ProductSelection.vue'),
			beforeEnter(to, from, next) {
				store.commit('setAddress', to.params.address)
				next()
			}
		},
		// {
		// 	path: '/location/:address/all',
		// 	name: 'location-all',
		// 	props: true,
		// 	component: () => import('../views/ProductShow.vue'),
		// 	beforeEnter(to, from, next) {
		// 		store.commit('setAddress', to.params.address)
		// 		store.commit('setCoordsFromMap', {
		// 			lat: to.query.lat,
		// 			lng: to.query.lng
		// 		})
		// 		next()
		// 	}
		// },
		{
			path: '/purchase',
			name: 'purchase',
			component: () => import('../views/Purchase.vue'),
		},
		{
			path: '/complete',
			name: 'complete',
			props: true,
			component: () => import('../views/Complete.vue'),
			// beforeEnter(to, from, next) {
			// 	store.commit('setOrderNumber', to.params.orderNumber)
			// 	next()
			// }
		},
		{
			path: '/invite_sent',
			name: 'inviteSent',
			props: true,
			component: () => import('../views/InviteSent.vue'),
			// beforeEnter(to, from, next) {
			// 	store.commit('setOrderNumber', to.params.orderNumber)
			// 	next()
			// }
		},
		{
			path: '/upgrade_sent',
			name: 'upgradeRequestSent',
			props: true,
			component: () => import('../views/UpgradeRequestSent.vue')
		},
		// {
		// 	path: '/legal',
		// 	name: 'legal',
		// 	props: true,
		// 	component: () => import('../views/Legal.vue')
		// },
		// {
		// 	path: '/legal/:type/:question',
		// 	name: 'legal-question',
		// 	props: true,
		// 	component: () => import('../views/LegalQuestion.vue')
		// },
		{
			path: '/notices',
			name: 'notices',
			props: true,
			component: () => import('../views/Notices.vue')
		},
		{
			path: '/faq',
			beforeEnter() {
				window.location.href = "http://help.cybersmart.co.za";
			}
		},
		{
			path: '/contact-us',
			name: 'contact-us',
			props: true,
			component: () => import('../views/ContactUs.vue')
		},
		{
			path: '/business-contact/:noProducts?',
			name: 'business-contact',
			props: true,
			component: () => import('../views/BusinessContact.vue')
		},
		{
			path: '/query_sent',
			name: 'querySent',
			props: true,
			component: () => import('../views/QuerySent.vue')
		},
		{
			path: '/blog',
			name: 'blog',
			props: true,
			component: () => import('../views/Blog.vue')
		},
		{
			path: '/blog/:id',
			name: 'blog-entry',
			props: true,
			component: () => import('../views/BlogEntry.vue')
		},
		// {
		// 	path: '/privacy-policy',
		// 	name: 'privacy-policy',
		// 	component: () => import('../views/PrivacyPolicy.vue')
		// },
		{
			path: '/resources',
			name: 'resources',
			component: () => import('../views/Resources.vue')
		},
		{
			path: '/404',
			name: 'FourOhFour',
			component: () => import('../views/FourOhFour.vue')
		},
		{
			path: '/:slug+',
			name: 'content-page',
			component: () => import('../views/ContentPage.vue'),
			props: true,
		},
		{
			path: '/:pathMatch(.*)*',
			redirect: '/'
		}
	],
	scrollBehavior(to, from, savedPosition) {
		// always scroll to top
		return { top: 0 }
	},
})

router.beforeEach((to, from) => {
	store.commit('setActiveSubmenu', null)
	store.commit('closeMobileMenuActive') //Ensure mobile nav closes on page change
	store.commit('setDarkNavigation', false) //Should default to light mode
})

router.afterEach((to, from) => {
	if (to.fullPath !== '/home-connectivity' && to.fullPath !== '/') {
		const { scrollPastElement } = useScroll();
		setTimeout(() => {
			scrollPastElement('feasibility-search', 160);
		}, 500);
	}
})

export default router