export default {extractCoordFromKml};

function extractCoordFromKml(kmlText) {
    //TODO: переписать покрасивее
    const myreg = /<LinearRing><coordinates>([0-9,.\- ]*)<\/coordinates><\/LinearRing>/g;
    const res = kmlText.match(myreg);
    //console.log("step1: ", res);

    if (res) {
        const myreg2 = /([-0-9.]+,[-0-9.]+)(,0)?\s/g;
        const res2 = res[0].match(myreg2);
        //console.log("step2: ", res2);

        if (res2){
            const myreg3 = /[-0-9.]+/g;
            const res3 = res2.map(res => res.match(myreg3));
            //console.log("step3: ", res3);

            if (res3) {
                const res4 = [...res3, res3[0]].map(curPair => [curPair[1], curPair[0]].join(",")).join("|");
                //console.log("kml: ", res, res2, res3, res4);
                return res4;
            }
        }
    }
}
