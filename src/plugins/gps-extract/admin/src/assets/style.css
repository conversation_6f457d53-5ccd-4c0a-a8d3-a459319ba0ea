body {
  margin: 0;
  padding: 0;
}
.header {
  background-color: #3498db;
  color: white;
  text-align: center;
  padding: 1em 0;
  margin-bottom: 25px;
}
.wrapper {
  display: flex;
  justify-content: center;
}
.date {
  width: 180px;
  height: 38px;
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: border-color 0.3s ease;
}
.submit {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  & button {
    display: inline-block;
    padding: 10px 20px;
    font-size: 16px;
    text-align: center;
    text-decoration: none;
    outline: none;
    color: #fff;
    background-color: #0097f7;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s;
    cursor: pointer;
    margin-left: 25px;
    &:hover {
      background-color: hsla(203, 100%, 48%, 0.7);
    }
  }
}
.choice {
  display: flex;
  justify-content: center;
  .choice__select {
    appearance: none;
    border: 1px solid #ccc;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: none;
    padding: 10px;
    font-size: 16px;
    color: #333;
    border-radius: 4px;
    cursor: pointer;
    width: 320px;
    margin-bottom: 20px;
  }
  &__select:focus {
    outline: none;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  }

  &__select option {
    background-color: #f1f1f1;
    color: #333;
  }
}
.response {
  display: flex;
  border-radius: 5px;
  color: #333;
  animation: fadeIn 0.5s ease-out forwards;
  padding: 15px;
  justify-content: center;
}
.responseText {
  width: 50%;

  text-align: center;
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 6px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}