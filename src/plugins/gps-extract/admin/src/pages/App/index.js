/**
 *
 * This component is the skeleton around the actual pages, and should only
 * contain code that should be seen on all pages. (e.g. navigation bar)
 *
 */

import React, { useState } from 'react';
import moment from "moment";
import axios from "axios";

import "../../assets/style.css";

// Simple email validation function
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const App = () => {
  const [email, setEmail] = useState("");;
  const [dateFrom, setDateFrom] = useState(moment().format('YYYY-MM-DD HH:mm:ss'));
  const [dateTo, setDateTo] = useState(moment().format('YYYY-MM-DD HH:mm:ss'));
  const [response, setResponse] = useState("");
  const [select, setSelect] = useState("feasibility check");
  const [disabled, setDisabled] = useState(false);


  async function handleSubmit(e) {
    e.preventDefault();
    console.log("TEST DATA 1", dateTo);
    console.log("TEST DATA 2", dateFrom);
    if (validateEmail(email)) {
      setResponse("");
      setDisabled(true);

      const token = sessionStorage.getItem('jwtToken');

      const response = await axios({
        method: 'POST',
        url: '/gps-extract/find',
        headers: {
          'Authorization': `Bearer ${token.replace(/"/g, '')}`,
          'Content-Type': 'application/json'
        },
        data: {
          dateFrom: dateFrom,
          dateTo: dateTo,
          email: email,
          type: select
        }
      });
      
      setDisabled(false);
      if (response.data.hasOwnProperty('errors')) {
        setResponse(response.data.errors);
      } else if (response.data.hasOwnProperty('arrayIsEmpty')) {
        setResponse("No results found based on your filter");
      } else {
        setResponse("Successfully sent. Check your mailbox");
      }
    } else {
      setResponse("Email is not valid. Please enter valid email");
    }
  }
  return (
    <section>
      <header className="header">
        <h1>Gps Extract Plugin</h1>
      </header>
      <div className="choice">
        <select className="choice__select" onChange={e => setSelect(e.target.value)} disabled="true">
          <option value="feasibility check" selected="selected">feasibility check</option>
        </select>
      </div>
      <div className="wrapper">
        <div>
          <input type="date" className="date" onChange={e => setDateFrom(moment(new Date(e.target.value)).set('hours', 0).set('minutes', 0).set('seconds', 0).format("YYYY-MM-DD HH:mm:ss"))} />
          <input type="date" className="date" onChange={e => setDateTo(moment(new Date(e.target.value)).set('hours', 23).set('minutes', 59).set('seconds', 59).format("YYYY-MM-DD HH:mm:ss"))} />
          <input type="text" className="date" placeholder="Enter your E-Mail" onChange={e => setEmail(e.target.value)} />
          <div className="submit">
            {!disabled ? <button className="submit" onClick={handleSubmit} disabled={disabled}>Send CSV</button> : 'Loading...'}
          </div>
        </div>
      </div>
      {response.length ? <div className="response">
        <div className="responseText"><span>{response}</span></div>
      </div> : ''}
    </section>
  );
};

export default App;
