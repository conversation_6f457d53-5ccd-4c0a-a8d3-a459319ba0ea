'use strict';

/**
 *  controller
 */

const pluginPkg = require("../../package.json");
const name = pluginPkg.strapi.name;

module.exports = ({ strapi }) => ({
	async getLogs(ctx) {
		try {
			console.log("getLogs request body:", ctx.request.body);
			const { dateFrom, dateTo, email, type } = ctx.request.body;

			const response = await strapi.plugins[name].services.myLogService.findData(dateFrom, dateTo, email, type);
			console.log("getLogs response:", response);

			ctx.status = 200;
			ctx.type = 'application/json';
			ctx.body = response;

			return response;
		} catch (error) {
			console.error("Error in getLogs controller:", error);
			ctx.status = 500;
			ctx.body = {
				success: false,
				error: error.message
			};
			return ctx.body;
		}
	}
});
