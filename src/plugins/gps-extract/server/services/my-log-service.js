'use strict';

const moment = require("moment");
const fs = require("fs").promises;
const path = require("path");
const os = require("os");

const FILE_NAME = "logs.csv";

const createCsvWriter = require('csv-writer').createObjectCsvWriter;

const pluginPkg = require ("../../package.json");
const name = pluginPkg.strapi.name;

module.exports = ({ strapi }) => ({
  async create(fields) {
    return strapi.query("plugin::gps-extract.log").create({
      data: fields
    });
  },
  async find(params) {
    return strapi.query('plugin::gps-extract.log').findMany(params);
  },
  async findData(dateFrom, dateTo, email, type) {
    let response = {};
    let filter = {
      type: type,
      dateC_gte: dateFrom,
      dateC_lte: dateTo,
    }

    const data = await strapi.query('plugin::gps-extract.log').findMany(filter);
	console.log("findData data:", data);
    const headers = [
      {
        id: "dateC",
        title: "date",
      },
      {
        id: "lat",
        title: "lat",
      },
      {
        id: "long",
        title: "long",
      },
    ];

    const valuesFiltered = data.filter((item, index) => {
      const itemDate = moment(item.dateC);
      const findEarlyCoords = data.some((obj, i) => {
        if (i !== index && item.lat === obj.lat && item.long === obj.long) {
          const objDate = moment(obj.dateC);
          if (itemDate.diff(obj.dateC, 'hours') < 24 && objDate.isBefore(itemDate)) {
            return true;
          }
        }
        return false;
      })
      return !findEarlyCoords;
    })

    try {
      if (valuesFiltered.length) {
        // Create CSV file in temp directory to avoid triggering file watcher
        const tempDir = os.tmpdir();
        const csvFilePath = path.join(tempDir, `${Date.now()}_${FILE_NAME}`);

        const csvWriter = createCsvWriter({
          path: csvFilePath,
          header: headers,
          fieldDelimiter: ';',
          fieldQuote: ''
        });
        await csvWriter.writeRecords(valuesFiltered);

        const emailSent = await strapi.plugins["email"].services.email.send({
          to: email,
          from: "<EMAIL>",
          subject: "[GPS Extract Plugin]: Coordinates Report",
          attachments: [
            {
              filename: `logs ${new Date()}.csv`,
              path: csvFilePath,
            }
          ]
        });
        console.log("Sending log Email Sent: ", emailSent);

        // Clean up temp file
        try {
          await fs.unlink(csvFilePath);
        } catch (unlinkError) {
          console.warn("Failed to delete temp CSV file:", unlinkError);
        }
      } else {
        response.arrayIsEmpty = true;
      }

      response.success = true;

      return response;

    } catch (Ex) {
      return  {
        success: false,
        errors: Ex
      };
    }
  }
});
