import { createStore } from 'vuex'
import marked from 'marked'
import axios from 'axios'
//import _ from "lodash";

// Vue.use(Vuex)

const store = createStore({
	state: {
		error: null,
		menuOpen: false,
		installType: 'new',
		provinces: {
			WC: 'Western Cape',
			EC: 'Eastern Cape',
			NC: 'Northern Cape',
			NW: 'North West',
			FS: 'Free State',
			KZN: 'KwaZulu-Natal',
			GP: 'Gauteng',
			LP: 'Limpopo',
			MP: 'Mpumalanga'
		},
		banks: [
			{ id: 0, name: "Please select a bank", branch_name: undefined },
			{ id: 1, name: "ABSA (ALLIED)", branch_name: undefined },
			{ id: 2, name: "ABSA (TRUST BANK)", branch_name: undefined },
			{ id: 3, name: "ABSA (UNITED)", branch_name: undefined },
			{ id: 4, name: "ABSA (VOLKSKAS)", branch_name: undefined },
			{ id: 5, name: "ABSA BANK", branch_name: "632005" },
			{ id: 6, name: "OLD MUTUAL BANK", branch_name: undefined },
			{ id: 7, name: "BANK OF ATHENS", branch_name: "410342" },
			{ id: 8, name: "BANK OF NAMIBIA", branch_name: undefined },
			{ id: 9, name: "BANK OF TAIWAN", branch_name: undefined },
			{ id: 10, name: "BANK OF TRANSKEI", branch_name: undefined },
			{ id: 11, name: "BANK OF WINDHOEK", branch_name: "486272" },
			{ id: 12, name: "BOLAND BANK", branch_name: undefined },
			{ id: 13, name: "CAPE OF GOOD HOPE", branch_name: undefined },
			{ id: 14, name: "CITIBANK", branch_name: "350005" },
			{ id: 15, name: "COMMUNITY BANK", branch_name: undefined },
			{ id: 16, name: "CREDIT AGRICOLE IN", branch_name: undefined },
			{ id: 17, name: "FIDELITY BANK", branch_name: "780117" },
			{ id: 18, name: "FIRST NATIONAL BANK", branch_name: "250655" },
			{ id: 19, name: "FRENCH BANK", branch_name: undefined },
			{ id: 20, name: "FUTURE BANK", branch_name: undefined },
			{ id: 21, name: "HABIB OVERSEAS BANK", branch_name: undefined },
			{ id: 22, name: "HBZ BANK LIMITED", branch_name: undefined },
			{ id: 23, name: "INVESTEC BANK LTD", branch_name: "580105" },
			{ id: 24, name: "LESOTHO BANK", branch_name: undefined },
			{ id: 25, name: "MERCANTILE BANK", branch_name: "450048" },
			{ id: 26, name: "NBS/ICAN", branch_name: undefined },
			{ id: 27, name: "NEDBANK/PERM", branch_name: "198765" },
			{ id: 28, name: "PERMANENT BANK", branch_name: undefined },
			{ id: 29, name: "RESERVE BANK", branch_name: "980172" },
			{ id: 30, name: "SAAMBOU/20TWENTY", branch_name: undefined },
			{ id: 31, name: "STANDARD BANK", branch_name: "051001" },
			{ id: 32, name: "SWABOU", branch_name: undefined },
			{ id: 33, name: "UNIBANK LIMITED", branch_name: "790005" },
			{ id: 34, name: "OLD MUTUAL BANK", branch_name: undefined },
			{ id: 35, name: "BOE BANK", branch_name: undefined },
			{ id: 36, name: "CAPITEC BANK", branch_name: "470010" },
			{ id: 37, name: "SA POST OFFICE", branch_name: "460005" },
			{ id: 38, name: "RAND MERCHANT BANK", branch_name: undefined },
			{ id: 39, name: "ABN AMRO BANK NV", branch_name: undefined },
			{ id: 40, name: "BIDVEST", branch_name: "462005" },
			{ id: 41, name: "HSBC", branch_name: "587000" },
			{ id: 42, name: "Societe General JHB Branch", branch_name: undefined },
			{ id: 43, name: "AFRICAN BANK", branch_name: undefined },
			{ id: 44, name: "UBANK LTD", branch_name: undefined },
			{ id: 45, name: "NEDBANK NAMIBIA", branch_name: undefined },
			{ id: 46, name: "BARCLAYS BANK", branch_name: undefined },
			{ id: 47, name: "ALBARAKA BANK", branch_name: undefined },
			{ id: 48, name: "STATE BANK OF INDIA", branch_name: undefined },
			{
				id: 49,
				name: "SWAZILAND DEV AND SAVINGS BANK",
				branch_name: undefined,
			},
			{ id: 50, name: "Olympus Mobile", branch_name: undefined },
			{ id: 51, name: "Cyberbills Clearing Bank", branch_name: undefined },
			{ id: 52, name: "SASFIN BANK LIMITED", branch_name: undefined },
		],
		titleIds: [
			{ value: 1, label: 'Mr' },
			{ value: 2, label: 'Mrs' },
			{ value: 3, label: 'Miss' },
			{ value: 4, label: 'Rev' },
			{ value: 5, label: 'Dr' },
			{ value: 6, label: 'Prof' },
			{ value: 7, label: 'Ms' },
			{ value: 8, label: 'Chief' },
			{ value: 9, label: 'Fr' },
			{ value: 11, label: 'N/A' },
		],
		userAccount: [],
		addresses: null,
		builtAddress: null,
		unitNumber: null,
		buildingName: null,
		addressList: null,
		zone: null,
		position: null,
		customer: {
			titleId: "",
			firstName: "",
			lastName: "",
			email: "",
			daytimeContactNumber: "",
			cellphone: "",
			idNumber: "",
			companyName: "",
			vatNumber: ""
		},
		agentId: undefined,
		address: undefined,
		addressCode: undefined,
		addressId: undefined,
		productSection: null,
		promoCodeId: null,
		selectedProduct: null,
		specifiedFullAddress: null,
		fullAddress: null,
		product: undefined,
		productIds: {},
		orderNumber: undefined,
		legal: null,
		gigaLiteView: false,
		coordsFromMap: {},
		privacyPolicy: localStorage.getItem("cookie_bar") || "",
		activeSubmenu: null,
		mobileMenuActive: false,
		darkNavigation: false,
		darkMenu: false,
	},
	getters: {
		getAllowedCookiePolicy: state => state.privacyPolicy,
		getGigaliteView: state => state.gigaLiteView,
		getTitleIds: state => state.titleIds,
		getMenuOpen: state => state.menuOpen,
		getLegal: state => state.legal,
		getSelectedProduct: state => state.selectedProduct,
		getProductSection: state => state.productSection,
		getInstallType: state => state.installType,
		getProvinces: state => state.provinces,
		getUnitNumber: state => state.unitNumber,
		getBuildingName: state => state.buildingName,
		getBuiltAddress: state => state.builtAddress,
		getAddressList: state => state.addressList,
		getSpecifiedFullAddress: state => state.specifiedFullAddress,
		getFullAddress: state => state.fullAddress,
		getBanks: state => state.banks,
		getZone: state => state.zone,
		getPosition: state => state.position,
		getCustomer: state => state.customer,
		getPromoCodeId: state => state.promoCodeId,
		getAgentId: state => state.agentId,
		getCoordsFromMap: state => state.coordsFromMap,
	},
	mutations: {
		setFreshSearch(state) {
			state.installType = 'new';
			state.addresses = null;
			state.builtAddress = null;
			state.unitNumber = null;
			state.buildingName = null;
			state.addressList = null;
			state.zone = null;
			state.position = null;
			state.address = undefined;
			state.addressCode = undefined;
			state.addressId = undefined;
			state.productSection = null;
			state.promoCodeId = null;
			state.selectedProduct = null;
			state.specifiedFullAddress = null;
			state.fullAddress = null;
			state.product = undefined;
			state.productIds = {};
			state.orderNumber = undefined;
			state.coordsFromMap = {};
		},
		setCoordsFromMap(state, newCoords) {
			state.coordsFromMap = newCoords;
		},
		setAllowedCookiePolicy(state) {
			state.privacyPolicy = "allowed";
			localStorage.setItem("cookie_bar", "allowed");
		},
		setGigaliteView(state, gigaliteView) {
			state.gigaLiteView = gigaliteView;
		},
		setPromoCodeId(state, promoCodeId) {
			state.promoCodeId = promoCodeId
		},
		setAddress(state, address) {
			state.address = address
		},
		setMenuOpen(state, menuOpen) {
			state.menuOpen = menuOpen
		},
		setCustomer(state, customer) {
			state.customer = customer
		},
		setPosition(state, position) {
			state.position = position
		},
		setZone(state, zone) {
			state.zone = zone
		},
		setAddressList(state, addressList) {
			state.addressList = addressList
		},
		setInstallType(state, installType) {
			state.installType = installType
		},
		setBuiltAddress(state, builtAddress) {
			state.builtAddress = builtAddress
		},
		setUnitNumber(state, unitNumber) {
			state.unitNumber = unitNumber
		},
		setBuildingName(state, buildingName) {
			state.buildingName = buildingName
		},

		setAddressCode(state, addressCode) {
			state.addressCode = addressCode
		},
		setAddressId(state, addressId) {
			state.addressId = addressId
		},
		setSpecifiedFullAddress(state, address) {
			state.specifiedFullAddress = address
		},
		setFullAddress(state, address) {
			state.fullAddress = address
		},
		setProduct(state, product) {
			state.product = product
		},
		setProductSection(state, product) {
			state.productSection = product
			state.selectedProduct = null
		},
		setSelectedProduct(state, product) {
			state.selectedProduct = product
		},
		setProductIds(state, data) {
			state.productIds = data
		},
		setOrderNumber(state, orderNumber) {
			state.orderNumber = orderNumber
		},
		setError(state, error) {
			state.error = error
		},
		setAgentId(state, agentId) {
			state.agentId = agentId;
		},
		setActiveSubmenu(state, id) {
			state.activeSubmenu = id;
		},
		toggleMobileMenuActive(state) {
			state.mobileMenuActive = !state.mobileMenuActive;
		},
		closeMobileMenuActive(state) {
			state.mobileMenuActive = false;
		},
		setDarkNavigation(state, value) {
			state.darkNavigation = value;
		},
		setDarkMenu(state, value) {
			state.darkMenu = value;
		}
	},
	actions: {
		async fetchLegal({ state }) {
			const response = await axios.get(`${import.meta.env.VITE_BACKEND_URL}legals`)
			state.legal = response.data.map(v => ({
				...v,
				html: marked(v.description)
			}))
		}
	}
})

export default store