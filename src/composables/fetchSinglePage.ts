import { onMounted, computed, ref, defineAsyncComponent, Ref } from 'vue';
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { useHead } from '@unhead/vue'

interface PageAttributes {
	seo: {
		metaTitle: string;
		metaDescription: string;
	};
}

interface PageData {
	id: number;
	attributes: PageAttributes;
}

export function useFetchSinglePage(pageName: string, darkNav: boolean = false) {
	const router = useRouter();
	const store = useStore();
	const page: Ref<PageData | null> = ref(null);
	const loading: Ref<boolean> = ref(true);

	const fetchPage = async () => {
		try {
			const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}${pageName}?populate=deep`);
			const data = await response.json();
			if (data.data.id > 0) {
				page.value = data.data;
				store.commit('setDarkNavigation', darkNav);
			} else {
				router.push('/404');
			}
		} catch (error) {
			console.error('An error occurred:', error);
		} finally {
			loading.value = false;
		}
	};

	// Reactive SEO meta
	const metaTitle = computed(() => {
		return page.value ? `Cybersmart | ${page.value.attributes.seo.metaTitle}` : 'Cybersmart | Page not found';
	});

	const metaDescription = computed(() => {
		return page.value ? page.value.attributes.seo.metaDescription : 'Page not found';
	});

	// Reactive head setup using computed getter
	useHead({
		title: metaTitle,
		meta: [
			{
				name: 'description',
				content: metaDescription,
			},
		],
	});

	onMounted(fetchPage);

	return {
		page,
		loading,
		fetchPage,
	};
}

export function useGetComponent(component: string) {
	const parts = component.split('.');
	const componentName = parts[1]
		.split('-')
		.map(word => word.charAt(0).toUpperCase() + word.slice(1))
		.join('');
	return defineAsyncComponent(() =>
		import(`@/blocks/${componentName}.vue`)
	);
}
