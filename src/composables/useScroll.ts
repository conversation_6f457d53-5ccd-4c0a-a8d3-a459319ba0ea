// useScroll.ts
import { onMounted, nextTick } from 'vue';

interface ScrollOptions {
    targetPosition: number;
    duration?: number;
    startPosition?: number;
    easing?: (t: number) => number;
}

export const EasingFunctions = {
    easeInOutQuad: (t: number): number => 
        t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
    
    easeInOutCubic: (t: number): number =>
        t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
    
    linear: (t: number): number => t
};

export const smoothScroll = async ({
    targetPosition,
    duration = 1000,
    startPosition = window.pageYOffset,
    easing = EasingFunctions.easeInOutQuad
}: ScrollOptions): Promise<void> => {
    return new Promise((resolve) => {
        let startTime: number | null = null;

        const animation = (currentTime: number): void => {
            if (startTime === null) startTime = currentTime;
            
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / duration, 1);
            
            const currentPosition = startPosition + 
                ((targetPosition - startPosition) * easing(progress));

            window.scrollTo(0, currentPosition);

            if (timeElapsed < duration) {
                requestAnimationFrame(animation);
            } else {
                resolve();
            }
        };

        requestAnimationFrame(animation);
    });
};

export const useScroll = () => {
    const scrollPastElement = async (elementId: string, padding = 20) => {
        await nextTick();
        const element = document.getElementById(elementId);
        
        if (!element) {
            console.warn(`Element with id '${elementId}' not found`);
            return;
		}
		
		const finalPosition = element.scrollHeight + padding;
        
        await smoothScroll({
            targetPosition: finalPosition,
            duration: 2000,
            easing: EasingFunctions.easeInOutCubic
        });
    };

    return {
        scrollPastElement,
        smoothScroll
    };
};