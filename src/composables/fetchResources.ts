import {ref, onMounted} from 'vue'

export function useFetchResources() {
	const resources = ref<any>(null)
	
	const fetchResources = async () => {
		try {
			const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}resource-categories?populate=deep`)
			const data = await response.json();
			resources.value = data
		} catch (error) {
			console.error('An error occurred:', error);
		}
	};

	onMounted(fetchResources)

	return { resources }
}