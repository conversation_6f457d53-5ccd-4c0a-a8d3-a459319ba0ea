import { ref } from 'vue';

export function useSubmitForm(formName: string, formData: object) {
	const isSubmitting = ref(false);
	const error = ref<string | null>(null);
	const success = ref<boolean>(false);

	const submitForm = async () => {
		isSubmitting.value = true;
		error.value = null;
		success.value = false;

		try {
			const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}ezforms/submit`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ formName, formData })
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const data = await response.json();
			success.value = true;
		} catch (err) {
			console.error('Error:', err);
			error.value = err.message;
		} finally {
			isSubmitting.value = false;
		}
	};

	return {
		isSubmitting,
		error,
		success,
		submitForm
	};
}