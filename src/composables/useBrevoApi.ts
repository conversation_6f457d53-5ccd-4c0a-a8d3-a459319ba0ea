interface BrevoContact {
	email: string;
	attributes: {
		FIRSTNAME?: string;
		LASTNAME?: string;
		PHONE?: string;
		COMPANY?: string;
		QUERY_TYPE?: string;
		MESSAGE?: string;
		[key: string]: any;
	};
	listIds?: number[];
}

interface BrevoError {
	code: string;
	message: string;
}

export const useBrevoApi = () => {
	const BREVO_API_KEY = import.meta.env.VITE_BREVO_API_KEY;
	const BREVO_API_URL = 'https://api.brevo.com/v3/contacts';

	const createContact = async (formData: any): Promise<boolean> => {
		try {
			// Split name into first and last name
			const nameParts = formData.name.split(' ');
			const firstName = nameParts[0];
			const lastName = nameParts.slice(1).join(' ');

			// Prepare contact data for Brevo
			const contactData: BrevoContact = {
				email: formData.email,
				attributes: {
					FIRSTNAME: firstName,
					LASTNAME: lastName || '',
					PHONE: formData.phone,
					COMPANY: formData.company,
					QUERY_TYPE: formData.query,
					MESSAGE: formData.message,
				},
				listIds: [6]
			};

			// Add conditional fields based on query type
			if (formData.faultType) {
				contactData.attributes.FAULT_TYPE = formData.faultType;
			}
			if (formData.fibreProvider) {
				contactData.attributes.FIBRE_PROVIDER = formData.fibreProvider;
			}
			if (formData.hostingFault) {
				contactData.attributes.HOSTING_FAULT = formData.hostingFault;
			}
			if (formData.cyberSmartAccountNumber) {
				contactData.attributes.ACCOUNT_NUMBER = formData.cyberSmartAccountNumber;
			}

			const response = await fetch(BREVO_API_URL, {
				method: 'POST',
				headers: {
					'Accept': 'application/json',
					'Content-Type': 'application/json',
					'api-key': BREVO_API_KEY
				},
				body: JSON.stringify(contactData)
			});

			if (!response.ok) {
				const errorData = await response.json() as BrevoError;

				// If the error is that the contact already exists, return true as this is acceptable
				if (errorData.message?.includes('email is already associated with another Contact')) {
					return true;
				}

				// For any other error, throw it
				throw new Error(errorData.message || 'Failed to create contact in Brevo');
			}

			return response.status === 201;
		} catch (error) {
			// Only log the error for debugging purposes
			console.error('Error creating contact in Brevo:', error);

			// Re-throw any error that isn't about existing contacts
			if (error instanceof Error && !error.message.includes('email is already associated with another Contact')) {
				throw error;
			}

			// Return true for existing contact case
			return true;
		}
	};

	return {
		createContact
	};
};